import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast_io.dart';
import 'habit.dart';
import 'section.dart';
import 'entry.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;
  // MULTI-SECTION TAGGING: Separate independent collections
  static const String _sectionsStoreName = 'sections';
  static const String _habitsStoreName = 'habits';
  static const String _entriesStoreName = 'entries'; // NEW: Entries collection

  final _sectionsStore = stringMapStoreFactory.store(_sectionsStoreName);
  final _habitsStore = stringMapStoreFactory.store(_habitsStoreName);
  final _entriesStore = stringMapStoreFactory.store(_entriesStoreName); // NEW: Entries store

  // COMPREHENSIVE DEBUGGING: Enhanced debugging system for error resolution
  static bool _debugMode = true; // Enable comprehensive debugging for error resolution

  // Enhanced debugging method with comprehensive logging
  void _debugLog(String message, {String? method, dynamic data}) {
    if (_debugMode) {
      final timestamp = DateTime.now().toIso8601String();
      final methodInfo = method != null ? '[$method] ' : '';
      debugPrint('[DEBUG] [$timestamp] DatabaseService: $methodInfo$message');
      if (data != null) {
        debugPrint('[DATA] $data');
      }
    }
  }

  // Method to toggle debug mode
  static void setDebugMode(bool enabled) {
    _debugMode = enabled;
    debugPrint('[DEBUG_MODE] DatabaseService: Debug mode ${enabled ? 'ENABLED' : 'DISABLED'}');
  }

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      _debugLog('Initializing database', method: '_initDatabase');
      
      // Get the application documents directory
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String dbPath = path.join(appDocDir.path, 'habits.db');
      
      _debugLog('Database path: $dbPath', method: '_initDatabase');

      // Open the database
      final db = await databaseFactoryIo.openDatabase(dbPath);
      _debugLog('Database initialized successfully', method: '_initDatabase');
      
      return db;
    } catch (e, stackTrace) {
      _debugLog('Error initializing database: $e', method: '_initDatabase');
      _debugLog('StackTrace: $stackTrace', method: '_initDatabase');
      rethrow;
    }
  }

  // MULTI-SECTION TAGGING: Save sections independently (no habits embedded)
  Future<void> saveAllSections(List<Section> sections) async {
    try {
      _debugLog('=== MULTI-SECTION TAGGING: SAVING SECTIONS WITH ORDER ===', method: 'saveAllSections');
      _debugLog('Saving ${sections.length} sections independently', method: 'saveAllSections');
      final db = await database;

      // Clear existing sections
      await _sectionsStore.delete(db);
      _debugLog('Cleared existing sections store', method: 'saveAllSections');

      // ORDER PERSISTENCE: Assign order indices to sections before saving
      final List<Section> orderedSections = [];
      for (int i = 0; i < sections.length; i++) {
        final section = sections[i];
        final orderedSection = section.copyWith(orderIndex: i);
        orderedSections.add(orderedSection);
        _debugLog('Assigned order index $i to section: ${section.name}', method: 'saveAllSections');
      }

      // Save each section with its ID as key and order index
      for (final section in orderedSections) {
        final sectionJson = section.toJson();
        await _sectionsStore.record(section.id).put(db, sectionJson);
        _debugLog('Saved section: ${section.name} (ID: ${section.id}) with order: ${section.orderIndex} and habitOrder: ${section.habitOrder}', method: 'saveAllSections', data: sectionJson);
      }
      _debugLog('Successfully saved all ${orderedSections.length} sections with order indices', method: 'saveAllSections');
    } catch (e, stackTrace) {
      _debugLog('Error saving sections: $e', method: 'saveAllSections');
      _debugLog('StackTrace: $stackTrace', method: 'saveAllSections');
      rethrow;
    }
  }

  // MULTI-SECTION TAGGING: Load sections independently with proper ordering
  Future<List<Section>> loadAllSections() async {
    try {
      _debugLog('=== MULTI-SECTION TAGGING: LOADING SECTIONS WITH ORDER ===', method: 'loadAllSections');
      _debugLog('Loading sections from independent collection', method: 'loadAllSections');
      final db = await database;

      final sectionsRecords = await _sectionsStore.find(db);
      _debugLog('Found ${sectionsRecords.length} section records', method: 'loadAllSections');

      if (sectionsRecords.isEmpty) {
        _debugLog('No sections found, creating and saving default section', method: 'loadAllSections');
        // Create and immediately save default section
        final defaultSection = Section(name: 'My Habits');
        _debugLog('Created default section: "${defaultSection.name}" (ID: ${defaultSection.id})', method: 'loadAllSections');
        
        // Save it immediately to the database
        await saveAllSections([defaultSection]);
        _debugLog('Saved default section to database', method: 'loadAllSections');
        
        return [defaultSection];
      }

      // Convert records back to Section objects
      final sections = <Section>[];
      for (final record in sectionsRecords) {
        try {
          _debugLog('Processing section record: ${record.key}', method: 'loadAllSections', data: record.value);
          final section = Section.fromJson(record.value);
          sections.add(section);
          _debugLog('Loaded section: ${section.name} (ID: ${section.id}) with order: ${section.orderIndex} and habitOrder: ${section.habitOrder}', method: 'loadAllSections');
        } catch (e) {
          _debugLog('Error parsing section record ${record.key}: $e', method: 'loadAllSections');
          _debugLog('Record data: ${record.value}', method: 'loadAllSections');
          // Continue with other records instead of rethrowing
        }
      }
      
      // ORDER PERSISTENCE: Sort sections by orderIndex to maintain correct order
      _debugLog('Sorting ${sections.length} sections by orderIndex', method: 'loadAllSections');
      sections.sort((a, b) => a.orderIndex.compareTo(b.orderIndex));
      
      // Debug log the final order
      _debugLog('Final section order after sorting:', method: 'loadAllSections');
      for (int i = 0; i < sections.length; i++) {
        _debugLog('[$i] ${sections[i].name} (order: ${sections[i].orderIndex})', method: 'loadAllSections');
      }
      
      _debugLog('Successfully loaded and sorted ${sections.length} sections', method: 'loadAllSections');
      return sections;
    } catch (e, stackTrace) {
      _debugLog('Error loading sections: $e', method: 'loadAllSections');
      _debugLog('StackTrace: $stackTrace', method: 'loadAllSections');
      
      // Return default section as fallback
      _debugLog('Returning default section as fallback', method: 'loadAllSections');
      return [Section(name: 'My Habits')];
    }
  }

  // MULTI-SECTION TAGGING: Save habits independently with order indices
  Future<void> saveAllHabits(List<Habit> habits) async {
    try {
      _debugLog('=== MULTI-SECTION TAGGING: SAVING HABITS WITH ORDER ===', method: 'saveAllHabits');
      _debugLog('Saving ${habits.length} habits independently', method: 'saveAllHabits');
      
      final db = await database;

      // Clear existing habits
      await _habitsStore.delete(db);
      _debugLog('Cleared existing habits store', method: 'saveAllHabits');

      // ORDER PERSISTENCE: Assign order indices to habits before saving
      final List<Habit> orderedHabits = [];
      for (int i = 0; i < habits.length; i++) {
        final habit = habits[i];
        final orderedHabit = habit.copyWith(orderIndex: i);
        orderedHabits.add(orderedHabit);
        _debugLog('Assigned order index $i to habit: ${habit.name}', method: 'saveAllHabits');
      }

      // Save each habit with its ID as key and order index
      for (final habit in orderedHabits) {
        final habitJson = habit.toJson();
        await _habitsStore.record(habit.id).put(db, habitJson);
        _debugLog('Saved habit: ${habit.name} (ID: ${habit.id}) with order: ${habit.orderIndex} and sections: ${habit.sectionIds}', method: 'saveAllHabits', data: habitJson);
      }
      _debugLog('Successfully saved all ${orderedHabits.length} habits with order indices', method: 'saveAllHabits');
    } catch (e, stackTrace) {
      _debugLog('Error saving habits: $e', method: 'saveAllHabits');
      _debugLog('StackTrace: $stackTrace', method: 'saveAllHabits');
      rethrow;
    }
  }

  // MULTI-SECTION TAGGING: Load habits independently with proper ordering
  Future<List<Habit>> loadAllHabits() async {
    try {
      _debugLog('=== MULTI-SECTION TAGGING: LOADING HABITS WITH ORDER ===', method: 'loadAllHabits');
      _debugLog('Loading habits from independent collection', method: 'loadAllHabits');
      final db = await database;

      final habitsRecords = await _habitsStore.find(db);
      _debugLog('Found ${habitsRecords.length} habit records', method: 'loadAllHabits');

      if (habitsRecords.isEmpty) {
        _debugLog('No habits found, returning empty list', method: 'loadAllHabits');
        return <Habit>[];
      }

      // Convert records back to Habit objects
      final habits = <Habit>[];
      for (final record in habitsRecords) {
        try {
          _debugLog('Processing habit record: ${record.key}', method: 'loadAllHabits', data: record.value);
          final habit = Habit.fromJson(record.value);
          habits.add(habit);
          _debugLog('Loaded habit: ${habit.name} (ID: ${habit.id}) with order: ${habit.orderIndex} and sections: ${habit.sectionIds}', method: 'loadAllHabits');
        } catch (e) {
          _debugLog('Error parsing habit record ${record.key}: $e', method: 'loadAllHabits');
          _debugLog('Record data: ${record.value}', method: 'loadAllHabits');
          // Continue with other records instead of rethrowing
        }
      }
      
      // ORDER PERSISTENCE: Sort habits by orderIndex to maintain correct order
      _debugLog('Sorting ${habits.length} habits by orderIndex', method: 'loadAllHabits');
      habits.sort((a, b) => a.orderIndex.compareTo(b.orderIndex));
      
      // Debug log the final order
      _debugLog('Final habit order after sorting:', method: 'loadAllHabits');
      for (int i = 0; i < habits.length; i++) {
        _debugLog('[$i] ${habits[i].name} (order: ${habits[i].orderIndex})', method: 'loadAllHabits');
      }
      
      _debugLog('Successfully loaded and sorted ${habits.length} habits', method: 'loadAllHabits');
      return habits;
    } catch (e, stackTrace) {
      _debugLog('Error loading habits: $e', method: 'loadAllHabits');
      _debugLog('StackTrace: $stackTrace', method: 'loadAllHabits');
      
      // Return empty list as fallback
      _debugLog('Returning empty list as fallback', method: 'loadAllHabits');
      return <Habit>[];
    }
  }

  // MULTI-SECTION TAGGING: Add habit with section IDs and proper order index
  Future<void> addHabit(Habit newHabit) async {
    try {
      _debugLog('=== MULTI-SECTION TAGGING: ADD HABIT WITH ORDER ===', method: 'addHabit');
      _debugLog('Adding habit: "${newHabit.name}" (ID: ${newHabit.id})', method: 'addHabit');
      _debugLog('Habit section IDs: ${newHabit.sectionIds}', method: 'addHabit', data: newHabit.toJson());
      
      // STREAMLINED: Get current habits count for order index without loading full list
      final db = await database;
      final habitsRecords = await _habitsStore.find(db);
      final nextOrderIndex = habitsRecords.length;
      
      // ANALYTICS: Ensure createdAt is set to current timestamp for new habits
      final orderedNewHabit = newHabit.copyWith(
        orderIndex: nextOrderIndex,
        createdAt: DateTime.now(),
      );
      _debugLog('Assigned order index $nextOrderIndex and createdAt timestamp to new habit', method: 'addHabit');
      
      // STREAMLINED: Save directly to database without loading all habits
      final habitJson = orderedNewHabit.toJson();
      await _habitsStore.record(orderedNewHabit.id).put(db, habitJson);
      _debugLog('Habit saved directly to database', method: 'addHabit');
      _debugLog('Successfully saved habit to database with order index', method: 'addHabit');
      
      // SECTION HABIT ORDER: Add habit ID to each section's habitOrder list
      if (orderedNewHabit.sectionIds.isNotEmpty) {
        _debugLog('Adding habit ID to section habitOrder lists', method: 'addHabit');
        
        for (final sectionId in orderedNewHabit.sectionIds) {
          // STREAMLINED: Update section directly without loading all sections
          final sectionRecord = await _sectionsStore.record(sectionId).get(db);
          if (sectionRecord != null) {
            final section = Section.fromJson(sectionRecord);
            if (!section.habitOrder.contains(orderedNewHabit.id)) {
              final updatedHabitOrder = List<String>.from(section.habitOrder);
              updatedHabitOrder.add(orderedNewHabit.id);
              final updatedSection = section.copyWith(habitOrder: updatedHabitOrder);
              await _sectionsStore.record(sectionId).put(db, updatedSection.toJson());
              _debugLog('Added habit ID ${orderedNewHabit.id} to section ${section.name} habitOrder', method: 'addHabit');
            }
          }
        }
      }
    } catch (e, stackTrace) {
      _debugLog('Error adding habit: $e', method: 'addHabit');
      _debugLog('StackTrace: $stackTrace', method: 'addHabit');
      rethrow;
    }
  }

  // MULTI-SECTION TAGGING: Update habit
  Future<void> updateHabit(Habit updatedHabit) async {
    try {
      _debugLog('=== MULTI-SECTION TAGGING: UPDATE HABIT ===', method: 'updateHabit');
      _debugLog('Updating habit: "${updatedHabit.name}" (ID: ${updatedHabit.id})', method: 'updateHabit');
      _debugLog('Habit section IDs: ${updatedHabit.sectionIds}', method: 'updateHabit', data: updatedHabit.toJson());
      
      // STREAMLINED: Update habit directly in database
      final db = await database;
      final existingRecord = await _habitsStore.record(updatedHabit.id).get(db);
      
      if (existingRecord != null) {
        // Update the habit directly
        await _habitsStore.record(updatedHabit.id).put(db, updatedHabit.toJson());
        _debugLog('Successfully updated habit directly in database', method: 'updateHabit');
      } else {
        _debugLog('Habit not found for update: ${updatedHabit.id}', method: 'updateHabit');
        throw Exception('Habit not found for update');
      }
    } catch (e, stackTrace) {
      _debugLog('Error updating habit: $e', method: 'updateHabit');
      _debugLog('StackTrace: $stackTrace', method: 'updateHabit');
      rethrow;
    }
  }

  // MULTI-SECTION TAGGING: Delete habit by ID
  Future<void> deleteHabit(String habitId) async {
    try {
      _debugLog('=== MULTI-SECTION TAGGING: DELETE HABIT BY ID ===', method: 'deleteHabit');
      _debugLog('Deleting habit with ID: $habitId', method: 'deleteHabit');
      
      // STREAMLINED: Delete habit directly from database
      final db = await database;
      final existingRecord = await _habitsStore.record(habitId).get(db);
      
      if (existingRecord != null) {
        final habitToDelete = Habit.fromJson(existingRecord);
        
        // Delete the habit directly
        await _habitsStore.record(habitId).delete(db);
        _debugLog('Removed habit "${habitToDelete.name}" directly from database', method: 'deleteHabit');
        
        // Also delete all entries for this habit
        await deleteEntriesForHabit(habitId);
        _debugLog('Deleted all entries for habit', method: 'deleteHabit');
        
        // STREAMLINED: Remove habit from section habitOrder lists directly
        final sectionsRecords = await _sectionsStore.find(db);
        for (final sectionRecord in sectionsRecords) {
          final section = Section.fromJson(sectionRecord.value);
          if (section.habitOrder.contains(habitId)) {
            final updatedHabitOrder = List<String>.from(section.habitOrder);
            updatedHabitOrder.remove(habitId);
            final updatedSection = section.copyWith(habitOrder: updatedHabitOrder);
            await _sectionsStore.record(section.id).put(db, updatedSection.toJson());
            _debugLog('Removed habit ID $habitId from section ${section.name} habitOrder', method: 'deleteHabit');
          }
        }
        
        _debugLog('Successfully deleted habit and updated sections', method: 'deleteHabit');
      } else {
        _debugLog('Habit not found for deletion: $habitId', method: 'deleteHabit');
        throw Exception('Habit not found for deletion');
      }
    } catch (e, stackTrace) {
      _debugLog('Error deleting habit: $e', method: 'deleteHabit');
      _debugLog('StackTrace: $stackTrace', method: 'deleteHabit');
      rethrow;
    }
  }

  // MULTI-SECTION TAGGING: Delete habit by object (backward compatibility)
  Future<void> deleteHabitByObject(Habit habitToDelete) async {
    return await deleteHabit(habitToDelete.id);
  }

  // Get habit by ID
  Future<Habit?> getHabitById(String habitId) async {
    try {
      _debugLog('Getting habit by ID: $habitId', method: 'getHabitById');
      final allHabits = await loadAllHabits();
      final habit = allHabits.where((h) => h.id == habitId).firstOrNull;
      if (habit != null) {
        _debugLog('Found habit: ${habit.name}', method: 'getHabitById');
      } else {
        _debugLog('Habit not found with ID: $habitId', method: 'getHabitById');
      }
      return habit;
    } catch (e, stackTrace) {
      _debugLog('Error getting habit by ID: $e', method: 'getHabitById');
      _debugLog('StackTrace: $stackTrace', method: 'getHabitById');
      return null;
    }
  }

  // MULTI-SECTION TAGGING: Helper method to get habits for a specific section
  List<Habit> getHabitsForSection(List<Habit> allHabits, String sectionId) {
    _debugLog('Getting habits for section: $sectionId', method: 'getHabitsForSection');
    final filteredHabits = allHabits.where((habit) => habit.belongsToSection(sectionId)).toList();
    _debugLog('Found ${filteredHabits.length} habits for section $sectionId', method: 'getHabitsForSection');
    return filteredHabits;
  }

  // MULTI-SECTION TAGGING: Helper method to get all habits (for "All" filter)
  List<Habit> getAllHabits(List<Habit> allHabits) {
    _debugLog('Getting all habits: ${allHabits.length}', method: 'getAllHabits');
    return allHabits;
  }

  // SECTION MANAGEMENT: Add new section to database with proper order index
  Future<void> addSection(Section newSection) async {
    try {
      _debugLog('=== SECTION MANAGEMENT: ADD SECTION WITH ORDER ===', method: 'addSection');
      _debugLog('Adding section: "${newSection.name}" (ID: ${newSection.id})', method: 'addSection');
      
      // Load current sections to determine next order index
      final currentSections = await loadAllSections();
      _debugLog('Current sections count: ${currentSections.length}', method: 'addSection');
      
      // ORDER PERSISTENCE: Assign the next available order index
      final nextOrderIndex = currentSections.length;
      final orderedNewSection = newSection.copyWith(orderIndex: nextOrderIndex);
      _debugLog('Assigned order index $nextOrderIndex to new section', method: 'addSection');
      
      final db = await database;
      
      // Save the new section with its ID as the key and order index
      await _sectionsStore.record(orderedNewSection.id).put(db, orderedNewSection.toJson());
      _debugLog('Successfully saved section to database with order index', method: 'addSection', data: orderedNewSection.toJson());
      
      _debugLog('=== ADD SECTION COMPLETE ===', method: 'addSection');
    } catch (e, stackTrace) {
      _debugLog('Error adding section: $e', method: 'addSection');
      _debugLog('StackTrace: $stackTrace', method: 'addSection');
      rethrow;
    }
  }

  // SECTION MANAGEMENT: Update existing section in database
  Future<void> updateSection(Section updatedSection) async {
    try {
      _debugLog('=== SECTION MANAGEMENT: UPDATE SECTION ===', method: 'updateSection');
      _debugLog('Updating section: "${updatedSection.name}" (ID: ${updatedSection.id})', method: 'updateSection');
      
      final db = await database;
      
      // Update the section with its ID as the key
      await _sectionsStore.record(updatedSection.id).put(db, updatedSection.toJson());
      _debugLog('Successfully updated section in database', method: 'updateSection', data: updatedSection.toJson());
      
      _debugLog('=== UPDATE SECTION COMPLETE ===', method: 'updateSection');
    } catch (e, stackTrace) {
      _debugLog('Error updating section: $e', method: 'updateSection');
      _debugLog('StackTrace: $stackTrace', method: 'updateSection');
      rethrow;
    }
  }

  // SECTION MANAGEMENT: Delete section from database
  Future<void> deleteSection(Section section) async {
    try {
      _debugLog('=== SECTION MANAGEMENT: DELETE SECTION ===', method: 'deleteSection');
      _debugLog('Deleting section: "${section.name}" (ID: ${section.id})', method: 'deleteSection');
      
      final db = await database;
      
      // Delete the section by its ID
      await _sectionsStore.record(section.id).delete(db);
      _debugLog('Successfully deleted section from database', method: 'deleteSection');
      
      _debugLog('=== DELETE SECTION COMPLETE ===', method: 'deleteSection');
    } catch (e, stackTrace) {
      _debugLog('Error deleting section: $e', method: 'deleteSection');
      _debugLog('StackTrace: $stackTrace', method: 'deleteSection');
      rethrow;
    }
  }

  // MULTI-SECTION TAGGING: Legacy compatibility method (delegates to new system)
  @Deprecated('Use loadAllHabits() instead')
  Future<List<Habit>> loadHabits() async {
    return await loadAllHabits();
  }

  // MULTI-SECTION TAGGING: Legacy compatibility method (delegates to new system)  
  @Deprecated('Use loadAllSections() instead')
  Future<List<Section>> loadSections() async {
    return await loadAllSections();
  }

  Future<void> close() async {
    try {
      _debugLog('Closing database', method: 'close');
      if (_database != null) {
        await _database!.close();
        _database = null;
        _debugLog('Database closed successfully', method: 'close');
      } else {
        _debugLog('Database was already null', method: 'close');
      }
    } catch (e, stackTrace) {
      _debugLog('Error closing database: $e', method: 'close');
      _debugLog('StackTrace: $stackTrace', method: 'close');
    }
  }

  // Additional debugging methods
  Future<void> printDatabaseInfo() async {
    try {
      _debugLog('=== DATABASE INFO ===', method: 'printDatabaseInfo');
      final db = await database;
      final records = await _habitsStore.find(db);
      _debugLog('Total records in database: ${records.length}', method: 'printDatabaseInfo');
      
      for (int i = 0; i < records.length; i++) {
        final record = records[i];
        _debugLog('Record $i - Key: ${record.key}', method: 'printDatabaseInfo', data: record.value);
      }
      _debugLog('=== END DATABASE INFO ===', method: 'printDatabaseInfo');
    } catch (e, stackTrace) {
      _debugLog('Error printing database info: $e', method: 'printDatabaseInfo');
      _debugLog('StackTrace: $stackTrace', method: 'printDatabaseInfo');
    }
  }

  // ============================================================================
  // ENTRY MANAGEMENT METHODS (NEW)
  // ============================================================================

  /// Save an entry to the database
  Future<void> saveEntry(Entry entry) async {
    try {
      _debugLog('Saving entry', method: 'saveEntry', data: entry.toJson());
      final db = await database;
      await _entriesStore.record(entry.id).put(db, entry.toJson());
      _debugLog('Entry saved successfully', method: 'saveEntry');
    } catch (e, stackTrace) {
      _debugLog('ERROR: Failed to save entry - $e', method: 'saveEntry');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  /// Load all entries for a specific habit
  Future<List<Entry>> loadEntriesForHabit(String habitId) async {
    try {
      _debugLog('Loading entries for habit: $habitId', method: 'loadEntriesForHabit');
      final db = await database;
      final finder = Finder(filter: Filter.equals('habitId', habitId));
      final records = await _entriesStore.find(db, finder: finder);
      
      final entries = records.map((record) {
        return Entry.fromJson(record.value);
      }).toList();
      
      // Sort by timestamp
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      _debugLog('Loaded ${entries.length} entries for habit', method: 'loadEntriesForHabit');
      return entries;
    } catch (e, stackTrace) {
      _debugLog('ERROR: Failed to load entries for habit - $e', method: 'loadEntriesForHabit');
      debugPrint('StackTrace: $stackTrace');
      return [];
    }
  }

  /// Load all entries from the database
  Future<List<Entry>> loadAllEntries() async {
    try {
      _debugLog('Loading all entries', method: 'loadAllEntries');
      final db = await database;
      final records = await _entriesStore.find(db);
      
      final entries = records.map((record) {
        return Entry.fromJson(record.value);
      }).toList();
      
      // Sort by timestamp
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      _debugLog('Loaded ${entries.length} total entries', method: 'loadAllEntries');
      return entries;
    } catch (e, stackTrace) {
      _debugLog('ERROR: Failed to load all entries - $e', method: 'loadAllEntries');
      debugPrint('StackTrace: $stackTrace');
      return [];
    }
  }

  /// Delete an entry from the database
  Future<void> deleteEntry(String entryId) async {
    try {
      _debugLog('Deleting entry: $entryId', method: 'deleteEntry');
      final db = await database;
      await _entriesStore.record(entryId).delete(db);
      _debugLog('Entry deleted successfully', method: 'deleteEntry');
    } catch (e, stackTrace) {
      _debugLog('ERROR: Failed to delete entry - $e', method: 'deleteEntry');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  /// Delete all entries for a specific habit
  Future<void> deleteEntriesForHabit(String habitId) async {
    try {
      _debugLog('Deleting all entries for habit: $habitId', method: 'deleteEntriesForHabit');
      final db = await database;
      final finder = Finder(filter: Filter.equals('habitId', habitId));
      await _entriesStore.delete(db, finder: finder);
      _debugLog('All entries deleted for habit', method: 'deleteEntriesForHabit');
    } catch (e, stackTrace) {
      _debugLog('ERROR: Failed to delete entries for habit - $e', method: 'deleteEntriesForHabit');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  /// Update the loadAllHabits method to also load entries for each habit
  Future<List<Habit>> loadAllHabitsWithEntries() async {
    try {
      _debugLog('Loading all habits with entries', method: 'loadAllHabitsWithEntries');
      
      // Load habits first
      final habits = await loadAllHabits();
      
      // Load entries for each habit
      for (final habit in habits) {
        final entries = await loadEntriesForHabit(habit.id);
        // Update the habit's entries list
        habit.entries.clear();
        habit.entries.addAll(entries);
      }
      
      _debugLog('Loaded ${habits.length} habits with entries', method: 'loadAllHabitsWithEntries');
      return habits;
    } catch (e, stackTrace) {
      _debugLog('ERROR: Failed to load habits with entries - $e', method: 'loadAllHabitsWithEntries');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  // COMPREHENSIVE DEBUGGING: Add missing saveHabit method that was being called by multiple files
  Future<void> saveHabit(Habit habit) async {
    try {
      _debugLog('=== COMPREHENSIVE DEBUGGING: SAVE HABIT METHOD ===', method: 'saveHabit');
      _debugLog('saveHabit called for habit: "${habit.name}" (ID: ${habit.id})', method: 'saveHabit');
      _debugLog('Habit data being saved:', method: 'saveHabit', data: habit.toJson());
      
      // Check if this is an existing habit or a new one
      final currentHabits = await loadAllHabits();
      final existingHabitIndex = currentHabits.indexWhere((h) => h.id == habit.id);
      
      if (existingHabitIndex != -1) {
        _debugLog('Existing habit found at index $existingHabitIndex, calling updateHabit', method: 'saveHabit');
        await updateHabit(habit);
        _debugLog('Successfully updated existing habit via updateHabit', method: 'saveHabit');
      } else {
        _debugLog('New habit detected, calling addHabit', method: 'saveHabit');
        await addHabit(habit);
        _debugLog('Successfully added new habit via addHabit', method: 'saveHabit');
      }
      
      _debugLog('=== SAVE HABIT COMPLETE ===', method: 'saveHabit');
    } catch (e, stackTrace) {
      _debugLog('ERROR: Failed to save habit - $e', method: 'saveHabit');
      _debugLog('StackTrace: $stackTrace', method: 'saveHabit');
      rethrow;
    }
  }
}