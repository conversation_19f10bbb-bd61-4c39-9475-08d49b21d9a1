import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';
import 'dart:developer' as developer;
import 'habit.dart';
import 'section.dart';
import 'entry.dart';
import 'status_indicator.dart';
import 'database_service.dart';
import 'table_row_data.dart';
import 'modern_theme.dart';
import 'enhanced_entry_dialog.dart';
import 'habit_analytics_screen.dart';

class EnhancedHabitTableView extends StatefulWidget {
  final List<Habit> habits;
  final List<DateTime> dates;
  final List<Section> sections;
  final Function(int oldIndex, int newIndex)? onReorder;
  final bool showReorderDialog;
  final bool showPercentageRow;
  final VoidCallback? onDataChanged;

  const EnhancedHabitTableView({
    super.key,
    required this.habits,
    required this.dates,
    required this.sections,
    this.onReorder,
    this.showReorderDialog = true,
    this.showPercentageRow = true,
    this.onDataChanged,
  });

  @override
  State<EnhancedHabitTableView> createState() => _EnhancedHabitTableViewState();
}

class _EnhancedHabitTableViewState extends State<EnhancedHabitTableView> {
  final _databaseService = DatabaseService();
  final List<TableRowData> _flatList = [];
  final ScrollController _verticalController = ScrollController();
  final ScrollController _horizontalController = ScrollController();

  @override
  void initState() {
    super.initState();
    developer.log('=== ENHANCED HABIT TABLE VIEW INIT STATE ===', name: 'EnhancedHabitTableView.Init');
    developer.log('Received ${widget.habits.length} habits, ${widget.dates.length} dates, ${widget.sections.length} sections', name: 'EnhancedHabitTableView.Init');
    
    // Log each received habit
    for (int i = 0; i < widget.habits.length; i++) {
      final habit = widget.habits[i];
      developer.log('Received Habit $i: "${habit.name}" (ID: ${habit.id})', name: 'EnhancedHabitTableView.Init');
    }
    
    _updateFlatList();
    developer.log('=== INIT STATE COMPLETE ===', name: 'EnhancedHabitTableView.Init');
  }

  @override
  void didUpdateWidget(EnhancedHabitTableView oldWidget) {
    super.didUpdateWidget(oldWidget);
    developer.log('=== ENHANCED HABIT TABLE VIEW DID UPDATE WIDGET ===', name: 'EnhancedHabitTableView.StateUpdate');
    developer.log('Old habits count: ${oldWidget.habits.length}, New habits count: ${widget.habits.length}', name: 'EnhancedHabitTableView.StateUpdate');
    developer.log('Habits list changed: ${oldWidget.habits != widget.habits}', name: 'EnhancedHabitTableView.StateUpdate');
    developer.log('Dates list changed: ${oldWidget.dates != widget.dates}', name: 'EnhancedHabitTableView.StateUpdate');
    
    // CRITICAL FIX: Check if the habits list has changed
    if (widget.habits != oldWidget.habits) {
      developer.log('CRITICAL FIX: Habits list changed, updating flat list.', name: 'EnhancedHabitTableView.StateUpdate');
      developer.log('Previous flat list size: ${_flatList.length}', name: 'EnhancedHabitTableView.StateUpdate');
      
      // Rebuild the internal flat list with the new data
      _updateFlatList();
      
      developer.log('New flat list size: ${_flatList.length}', name: 'EnhancedHabitTableView.StateUpdate');
      developer.log('Flat list successfully updated with new habits data', name: 'EnhancedHabitTableView.StateUpdate');
    }
    
    // Also check if dates changed (for column updates)
    if (oldWidget.dates != widget.dates) {
      developer.log('Dates list changed, updating flat list for new date range.', name: 'EnhancedHabitTableView.StateUpdate');
      _updateFlatList();
    }
    
    developer.log('=== DID UPDATE WIDGET COMPLETE ===', name: 'EnhancedHabitTableView.StateUpdate');
  }

  @override
  void dispose() {
    _verticalController.dispose();
    _horizontalController.dispose();
    super.dispose();
  }

  void _updateFlatList() {
    developer.log('=== UPDATING FLAT LIST ===', name: 'EnhancedHabitTableView.FlatList');
    developer.log('Updating flat list with ${widget.habits.length} habits', name: 'EnhancedHabitTableView.FlatList');
    
    _flatList.clear();
    
    for (int habitIndex = 0; habitIndex < widget.habits.length; habitIndex++) {
      final habit = widget.habits[habitIndex];
      developer.log('Processing habit $habitIndex: "${habit.name}" (Sections: ${habit.sectionIds})', name: 'EnhancedHabitTableView.FlatList');
      
      Section? parentSection;
      if (habit.sectionIds.isNotEmpty && widget.sections.isNotEmpty) {
        parentSection = widget.sections.firstWhere(
          (section) => section.id == habit.sectionIds.first,
          orElse: () => Section(name: 'Unknown'),
        );
        developer.log('Found parent section: "${parentSection.name}" for habit "${habit.name}"', name: 'EnhancedHabitTableView.FlatList');
      } else {
        parentSection = Section(name: 'Unknown');
        developer.log('Using Unknown section for habit "${habit.name}"', name: 'EnhancedHabitTableView.FlatList');
      }
      
      _flatList.add(HabitDataRow(habit, parentSection));
      developer.log('Added HabitDataRow for "${habit.name}" to flat list', name: 'EnhancedHabitTableView.FlatList');
    }
    
    developer.log('Flat list updated with ${_flatList.length} rows', name: 'EnhancedHabitTableView.FlatList');
    developer.log('=== FLAT LIST UPDATE COMPLETE ===', name: 'EnhancedHabitTableView.FlatList');
  }

  // Enhanced percentage calculation with better accuracy
  int _calculateCompletionPercentage(DateTime date) {
    if (widget.habits.isEmpty) return 0;

    int completedCount = 0;
    for (final habit in widget.habits) {
      if (habit.isCompletedOnDate(date)) {
        completedCount++;
      }
    }

    return ((completedCount / widget.habits.length) * 100).round();
  }

  @override
  Widget build(BuildContext context) {
    developer.log('=== BUILDING ENHANCED HABIT TABLE VIEW ===', name: 'EnhancedHabitTableView.Build');
    developer.log('Building with ${widget.habits.length} habits and ${widget.dates.length} dates', name: 'EnhancedHabitTableView.Build');
    developer.log('Flat list contains ${_flatList.length} rows', name: 'EnhancedHabitTableView.Build');
    
    if (widget.habits.isEmpty) {
      developer.log('WARNING: No habits to display - returning empty state', name: 'EnhancedHabitTableView.Build');
      return const Center(
        child: Text('No habits to display'),
      );
    }

    final theme = Theme.of(context);
    final totalRows = widget.habits.length + 2; // +2 for percentage and date header rows
    final totalColumns = widget.dates.length + 1; // +1 for habit name column
    
    developer.log('Table dimensions: ${totalRows} rows x ${totalColumns} columns', name: 'EnhancedHabitTableView.Build');
    developer.log('Show percentage row: ${widget.showPercentageRow}', name: 'EnhancedHabitTableView.Build');

    return TableView.builder(
      verticalDetails: ScrollableDetails.vertical(
        controller: _verticalController,
      ),
      horizontalDetails: ScrollableDetails.horizontal(
        controller: _horizontalController,
      ),
      pinnedColumnCount: 1, // Fix Task 2: Freeze the first column (habit names)
      pinnedRowCount: 2, // Fix Task 2: Freeze the top two rows (percentage and date headers)
      cellBuilder: (context, vicinity) {
        try {
          return _buildCellFlattened(context, vicinity);
        } catch (e, stackTrace) {
          developer.log('Error building cell at ${vicinity.row}, ${vicinity.column}: $e', name: 'EnhancedHabitTableView');
          developer.log('StackTrace: $stackTrace', name: 'EnhancedHabitTableView');
          return _buildErrorCell('Error: $e');
        }
      },
      columnCount: totalColumns,
      rowCount: totalRows, // totalRows already includes both header rows
      columnBuilder: (index) => TableSpan(
        extent: index == 0 
            ? const FixedTableSpanExtent(200) // Habit name column
            : const FixedTableSpanExtent(50), // Date columns
      ),
      rowBuilder: (index) => const TableSpan(
        extent: FixedTableSpanExtent(48), // Fixed row height
      ),
    );
  }

  TableViewCell _buildCellFlattened(BuildContext context, TableVicinity vicinity) {
    // Task 1: Add detailed logging at the beginning
    developer.log('=== _buildCellFlattened called ===', name: 'EnhancedHabitTableView.CellBuilder');
    developer.log('rowIndex: ${vicinity.row}, columnIndex: ${vicinity.column}', name: 'EnhancedHabitTableView.CellBuilder');
    developer.log('_flatList.length: ${_flatList.length}', name: 'EnhancedHabitTableView.CellBuilder');
    developer.log('widget.habits.length: ${widget.habits.length}', name: 'EnhancedHabitTableView.CellBuilder');
    developer.log('widget.dates.length: ${widget.dates.length}', name: 'EnhancedHabitTableView.CellBuilder');
    
    final theme = Theme.of(context);
    
    // Task 3: Correct Header Row Order - Percentage Header Row first (row 0)
    if (vicinity.row == 0) {
      developer.log('Building Percentage Header Row', name: 'EnhancedHabitTableView.CellBuilder');
      if (vicinity.column == 0) {
        return _buildPercentageLabelCell();
      } else {
        final dateIndex = vicinity.column - 1;
        if (dateIndex >= 0 && dateIndex < widget.dates.length) {
          return _buildPercentageCell(dateIndex);
        }
        return _buildErrorCell('Invalid percentage column');
      }
    }

    // Task 3: Correct Header Row Order - Date Header Row second (row 1)
    if (vicinity.row == 1) {
      developer.log('Building Date Header Row', name: 'EnhancedHabitTableView.CellBuilder');
      if (vicinity.column == 0) {
        return _buildHeaderCornerCell();
      } else {
        final dateIndex = vicinity.column - 1;
        if (dateIndex >= 0 && dateIndex < widget.dates.length) {
          return _buildDateHeaderCell(dateIndex);
        }
        return _buildErrorCell('Invalid date column');
      }
    }

    // Task 1: Validate Indexing - Account for both header rows
    final flatListIndex = vicinity.row - 2; // Subtract 2 for both percentage and date headers
    developer.log('Calculated flatListIndex: $flatListIndex (vicinity.row - 2)', name: 'EnhancedHabitTableView.CellBuilder');
    
    // Task 1: Trace the HabitDataRow case
    if (flatListIndex >= 0 && flatListIndex < _flatList.length) {
      final rowData = _flatList[flatListIndex];
      developer.log('Retrieved rowData at index $flatListIndex: ${rowData.runtimeType}', name: 'EnhancedHabitTableView.CellBuilder');
      
      if (rowData is HabitDataRow) {
        developer.log('Processing HabitDataRow for habit: "${rowData.habit.name}"', name: 'EnhancedHabitTableView.CellBuilder');
        
        if (vicinity.column == 0) {
          developer.log('Building habit name cell for: "${rowData.habit.name}"', name: 'EnhancedHabitTableView.CellBuilder');
          return _buildHabitNameCell(rowData.habit, flatListIndex);
        } else {
          final dateIndex = vicinity.column - 1;
          if (dateIndex >= 0 && dateIndex < widget.dates.length) {
            final date = widget.dates[dateIndex];
            developer.log('Building status cell for habit: "${rowData.habit.name}" on date: $date', name: 'EnhancedHabitTableView.CellBuilder');
            return _buildStatusCell(rowData.habit, date);
          }
          developer.log('Invalid date index: $dateIndex', name: 'EnhancedHabitTableView.CellBuilder');
          return _buildErrorCell('Invalid date index');
        }
      } else {
        developer.log('ERROR: rowData is not HabitDataRow, it is: ${rowData.runtimeType}', name: 'EnhancedHabitTableView.CellBuilder');
        return _buildErrorCell('Invalid row data type');
      }
    } else {
      developer.log('ERROR: flatListIndex $flatListIndex is out of bounds (0-${_flatList.length - 1})', name: 'EnhancedHabitTableView.CellBuilder');
      return _buildErrorCell('Index out of bounds');
    }
  }

  TableViewCell _buildHeaderCornerCell() {
    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant,
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: const Center(
          child: Icon(Icons.calendar_today, size: 16),
        ),
      ),
    );
  }

  TableViewCell _buildDateHeaderCell(int columnIndex) {
    if (columnIndex >= widget.dates.length) {
      return _buildErrorCell('Invalid date index');
    }

    final date = widget.dates[columnIndex];
    final theme = Theme.of(context);
    final isToday = _isSameDay(date, DateTime.now());
    
    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: isToday 
              ? theme.colorScheme.primary.withOpacity(0.1)
              : theme.colorScheme.surfaceVariant,
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _formatDayOfWeek(date),
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: isToday 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              date.day.toString(),
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: isToday ? FontWeight.w600 : FontWeight.w500,
                color: isToday 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }

  TableViewCell _buildPercentageLabelCell() {
    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant,
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: Center(
          child: Text(
            'Completion %',
            style: GoogleFonts.inter(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ),
    );
  }

  TableViewCell _buildPercentageCell(int columnIndex) {
    if (columnIndex >= widget.dates.length) {
      return _buildErrorCell('Invalid percentage index');
    }

    final date = widget.dates[columnIndex];
    final percentage = _calculateCompletionPercentage(date);
    final theme = Theme.of(context);
    
    Color percentageColor;
    if (percentage == 0) {
      percentageColor = theme.colorScheme.onSurfaceVariant;
    } else if (percentage <= 33) {
      percentageColor = Colors.red;
    } else if (percentage <= 66) {
      percentageColor = Colors.orange;
    } else if (percentage <= 89) {
      percentageColor = Colors.blue;
    } else {
      percentageColor = Colors.green;
    }

    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceVariant,
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: Center(
          child: Text(
            '$percentage%',
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: percentageColor,
            ),
          ),
        ),
      ),
    );
  }

  TableViewCell _buildHabitNameCell(Habit habit, int habitIndex) {
    final theme = Theme.of(context);
    final section = widget.sections.firstWhere(
      (s) => habit.sectionIds.contains(s.id),
      orElse: () => Section(name: 'Unknown'),
    );

    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: InkWell(
          onTap: () => _showHabitDetails(habit),
          onLongPress: widget.showReorderDialog ? () => _showReorderDialog(habitIndex) : null,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Row(
              children: [
                // Section color indicator
                Container(
                  width: 3,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _getSectionColor(section),
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
                const SizedBox(width: 8),
                // Habit name
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        habit.name,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (habit.type == HabitType.numerical && habit.unit != null)
                        Text(
                          '${habit.targetValue?.toInt() ?? 0} ${habit.unit}',
                          style: GoogleFonts.inter(
                            fontSize: 9,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  TableViewCell _buildStatusCell(Habit habit, DateTime date) {
    final theme = Theme.of(context);
    final isCompleted = habit.isCompletedOnDate(date);
    final isToday = _isSameDay(date, DateTime.now());

    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: isToday 
              ? theme.colorScheme.primary.withOpacity(0.05)
              : theme.colorScheme.surface,
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: InkWell(
          onTap: () => _toggleHabitCompletion(habit, date),
          child: Center(
            child: habit.type == HabitType.boolean
                ? Icon(
                    isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                    size: 20,
                    color: isCompleted 
                        ? Colors.green
                        : theme.colorScheme.onSurfaceVariant,
                  )
                : _buildNumericalIndicator(habit, date, isCompleted),
          ),
        ),
      ),
    );
  }

  Widget _buildNumericalIndicator(Habit habit, DateTime date, bool isCompleted) {
    final theme = Theme.of(context);
    final entry = habit.getEntryForDate(date);
    final value = entry?.value ?? 0.0;
    final target = habit.targetValue ?? 1.0;
    final percentage = target > 0 ? (value / target).clamp(0.0, 1.0) : 0.0;

    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isCompleted 
            ? Colors.green.withOpacity(0.2)
            : theme.colorScheme.surfaceVariant,
        border: Border.all(
          color: isCompleted ? Colors.green : theme.colorScheme.outline,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          value.toInt().toString(),
          style: GoogleFonts.inter(
            fontSize: 8,
            fontWeight: FontWeight.w600,
            color: isCompleted ? Colors.green : theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }

  TableViewCell _buildErrorCell(String message) {
    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          border: Border.all(color: Colors.red, width: 0.5),
        ),
        child: Center(
          child: Text(
            'Error',
            style: GoogleFonts.inter(
              fontSize: 10,
              color: Colors.red,
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _toggleHabitCompletion(Habit habit, DateTime date) async {
    try {
      developer.log('Toggling completion for habit: ${habit.name} on date: $date', name: 'EnhancedHabitTableView');
      
      if (habit.type == HabitType.boolean) {
        // For boolean habits, toggle completion directly
        final isCurrentlyCompleted = habit.isCompletedOnDate(date);
        final newCompletionStatus = !isCurrentlyCompleted;
        
        developer.log('Current completion status: $isCurrentlyCompleted, new status: $newCompletionStatus', name: 'EnhancedHabitTableView');
        
        // Create or update entry for this date
        final entry = Entry(
          habitId: habit.id,
          timestamp: date,
          value: newCompletionStatus,
          type: EntryType.boolean,
        );
        
        // Save entry to database
        await _databaseService.saveEntry(entry);
        
        // Update habit's entries
        habit.addEntry(entry);
        
        // Save updated habit
        await _databaseService.saveHabit(habit);
        
        developer.log('Boolean habit completion toggled successfully', name: 'EnhancedHabitTableView');
      } else {
        // For numerical habits, show entry dialog
        await _showEnhancedEntryDialog(habit, date);
      }

      // Trigger data reload
      if (widget.onDataChanged != null) {
        widget.onDataChanged!();
      }
      
      developer.log('Successfully toggled habit completion', name: 'EnhancedHabitTableView');
    } catch (e, stackTrace) {
      developer.log('Error toggling habit completion: $e', name: 'EnhancedHabitTableView');
      developer.log('StackTrace: $stackTrace', name: 'EnhancedHabitTableView');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update habit: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showEnhancedEntryDialog(Habit habit, DateTime date) async {
    if (!mounted) return;

    developer.log('Showing enhanced entry dialog for habit: ${habit.name} on date: $date', name: 'EnhancedHabitTableView');
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => EnhancedEntryDialog(
        habit: habit,
        date: date,
        existingEntry: habit.getEntryForDate(date),
      ),
    );
    
    // If dialog returned true (indicating successful save), trigger data reload
    if (result == true && widget.onDataChanged != null) {
      developer.log('Entry dialog completed successfully, triggering data reload', name: 'EnhancedHabitTableView');
      widget.onDataChanged!();
    }
  }

  void _showHabitDetails(Habit habit) {
    // Navigate to habit analytics screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HabitAnalyticsScreen(habit: habit),
      ),
    );
  }

  void _showReorderDialog(int habitIndex) {
    if (widget.onReorder == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reorder Habit'),
        content: Text('Move "${widget.habits[habitIndex].name}" to a new position?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Show position selector or implement drag-and-drop
            },
            child: const Text('Move'),
          ),
        ],
      ),
    );
  }

  Color _getSectionColor(Section section) {
    try {
      return Color(int.parse(section.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      return isDark ? ModernTheme.darkAccent : ModernTheme.lightAccent;
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  String _formatDayOfWeek(DateTime date) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.weekday % 7];
  }
}