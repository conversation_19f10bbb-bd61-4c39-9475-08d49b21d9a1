import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;

/// Enhanced circular progress widget with animations and color coding
class AnimatedCircularScore extends StatefulWidget {
  final double score; // 0-100
  final double size;
  final Color? color;
  final Duration animationDuration;
  final bool showPercentage;

  const AnimatedCircularScore({
    super.key,
    required this.score,
    this.size = 80,
    this.color,
    this.animationDuration = const Duration(milliseconds: 1500),
    this.showPercentage = true,
  });

  @override
  State<AnimatedCircularScore> createState() => _AnimatedCircularScoreState();
}

class _AnimatedCircularScoreState extends State<AnimatedCircularScore>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.score / 100,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCircularScore oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.score != widget.score) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.score / 100,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final scoreColor = widget.color ?? _getScoreColor(widget.score);
    
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Background circle
              SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: widget.size * 0.08,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  ),
                ),
              ),
              
              // Progress circle
              SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: _animation.value,
                  strokeWidth: widget.size * 0.08,
                  valueColor: AlwaysStoppedAnimation<Color>(scoreColor),
                  strokeCap: StrokeCap.round,
                ),
              ),
              
              // Score text
              if (widget.showPercentage)
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${(widget.score * _animation.value).round()}%',
                      style: GoogleFonts.inter(
                        fontSize: widget.size * 0.2,
                        fontWeight: FontWeight.w700,
                        color: scoreColor,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.blue;
    if (score >= 50) return Colors.orange;
    return Colors.red;
  }
}

/// Mini sparkline chart for showing trend data
class MiniSparkline extends StatelessWidget {
  final List<double> scores;
  final double width;
  final double height;
  final Color color;

  const MiniSparkline({
    super.key,
    required this.scores,
    this.width = 60,
    this.height = 20,
    this.color = Colors.blue,
  });

  @override
  Widget build(BuildContext context) {
    if (scores.isEmpty) {
      return SizedBox(width: width, height: height);
    }

    return CustomPaint(
      size: Size(width, height),
      painter: _SparklinePainter(scores, color),
    );
  }
}

class _SparklinePainter extends CustomPainter {
  final List<double> scores;
  final Color color;

  _SparklinePainter(this.scores, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    if (scores.length < 2) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final path = Path();
    final maxScore = scores.reduce(math.max);
    final minScore = scores.reduce(math.min);
    final range = maxScore - minScore;

    if (range == 0) {
      // All scores are the same, draw a horizontal line
      path.moveTo(0, size.height / 2);
      path.lineTo(size.width, size.height / 2);
    } else {
      for (int i = 0; i < scores.length; i++) {
        final x = (i / (scores.length - 1)) * size.width;
        final normalizedScore = (scores[i] - minScore) / range;
        final y = size.height - (normalizedScore * size.height);

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Enhanced streak display with milestone animations
class AnimatedStreakDisplay extends StatefulWidget {
  final int currentStreak;
  final int bestStreak;
  final bool showMilestoneAnimation;

  const AnimatedStreakDisplay({
    super.key,
    required this.currentStreak,
    required this.bestStreak,
    this.showMilestoneAnimation = true,
  });

  @override
  State<AnimatedStreakDisplay> createState() => _AnimatedStreakDisplayState();
}

class _AnimatedStreakDisplayState extends State<AnimatedStreakDisplay>
    with TickerProviderStateMixin {
  late AnimationController _countController;
  late AnimationController _milestoneController;
  late Animation<int> _countAnimation;
  late Animation<double> _scaleAnimation;
  int _previousStreak = 0;

  @override
  void initState() {
    super.initState();
    _previousStreak = widget.currentStreak;
    
    _countController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _milestoneController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _countAnimation = IntTween(
      begin: 0,
      end: widget.currentStreak,
    ).animate(CurvedAnimation(
      parent: _countController,
      curve: Curves.easeOutCubic,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _milestoneController,
      curve: Curves.elasticOut,
    ));
    
    _countController.forward();
  }

  @override
  void didUpdateWidget(AnimatedStreakDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.currentStreak != widget.currentStreak) {
      // Check for milestone achievement
      if (widget.showMilestoneAnimation && _isMilestone(widget.currentStreak)) {
        _milestoneController.forward().then((_) {
          _milestoneController.reverse();
        });
      }
      
      _countAnimation = IntTween(
        begin: _previousStreak,
        end: widget.currentStreak,
      ).animate(CurvedAnimation(
        parent: _countController,
        curve: Curves.easeOutCubic,
      ));
      
      _countController.forward(from: 0);
      _previousStreak = widget.currentStreak;
    }
  }

  bool _isMilestone(int streak) {
    return streak > 0 && (streak == 7 || streak == 30 || streak == 100 || streak % 100 == 0);
  }

  @override
  void dispose() {
    _countController.dispose();
    _milestoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_countAnimation, _scaleAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: _getStreakColor(widget.currentStreak).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _getStreakColor(widget.currentStreak).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('🔥', style: TextStyle(fontSize: 16)),
                const SizedBox(width: 6),
                Text(
                  '${_countAnimation.value}',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: _getStreakColor(widget.currentStreak),
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  widget.currentStreak == 1 ? 'day' : 'days',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: _getStreakColor(widget.currentStreak),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getStreakColor(int streak) {
    if (streak >= 100) return Colors.purple;
    if (streak >= 30) return Colors.orange;
    if (streak >= 7) return Colors.green;
    if (streak >= 3) return Colors.blue;
    return Colors.grey;
  }
}

/// Compact score and streak widget for list items
class CompactHabitMetrics extends StatelessWidget {
  final double? score;
  final int streak;
  final List<double>? scoreHistory;
  final bool showSparkline;

  const CompactHabitMetrics({
    super.key,
    this.score,
    required this.streak,
    this.scoreHistory,
    this.showSparkline = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Score display
        if (score != null) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getScoreColor(score!).withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '${score!.round()}%',
              style: GoogleFonts.inter(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: _getScoreColor(score!),
              ),
            ),
          ),
          const SizedBox(width: 6),
        ],
        
        // Streak display with fire emoji
        if (streak > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getStreakColor(streak).withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '🔥$streak',
              style: GoogleFonts.inter(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: _getStreakColor(streak),
              ),
            ),
          ),
        ],
        
        if (showSparkline && scoreHistory != null && scoreHistory!.isNotEmpty) ...[
          const SizedBox(width: 8),
          MiniSparkline(
            scores: scoreHistory!,
            width: 40,
            height: 16,
            color: theme.colorScheme.primary.withOpacity(0.7),
          ),
        ],
      ],
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.blue;
    if (score >= 50) return Colors.orange;
    return Colors.red;
  }

  Color _getStreakColor(int streak) {
    if (streak >= 30) return Colors.purple;
    if (streak >= 14) return Colors.orange;
    if (streak >= 7) return Colors.green;
    if (streak >= 3) return Colors.blue;
    return Colors.grey;
  }
}