import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:developer' as developer;
import 'habit.dart';
import 'section.dart';
import 'database_service.dart';
import 'habit_table_view.dart';
import 'modern_theme.dart';
import 'enhanced_habit_table_view.dart';

class HabitsInSectionScreen extends StatefulWidget {
  final Section section;

  const HabitsInSectionScreen({
    super.key,
    required this.section,
  });

  @override
  State<HabitsInSectionScreen> createState() => _HabitsInSectionScreenState();
}

class _HabitsInSectionScreenState extends State<HabitsInSectionScreen> {
  final _databaseService = DatabaseService();
  
  late Future<void> _dataFuture;
  List<Habit> _allHabits = [];
  List<Section> _allSections = [];
  List<Habit> _sectionHabits = [];

  @override
  void initState() {
    super.initState();
    developer.log('HabitsInSectionScreen initialized for section: ${widget.section.name}', name: 'HabitsInSectionScreen');
    _dataFuture = _loadData();
  }

  Future<void> _loadData() async {
    try {
      final results = await Future.wait([
        _databaseService.loadAllHabits(),
        _databaseService.loadAllSections(),
      ]);

      _allHabits = results[0] as List<Habit>;
      _allSections = results[1] as List<Section>;

      developer.log('Total habits in this section: ${_allHabits.where((habit) => habit.sectionIds.contains(widget.section.id)).length}', name: 'HabitsInSectionScreen');
      
      _filterHabitsForSection();
    } catch (e, stackTrace) {
      debugPrint('ERROR: Failed to load data - $e');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  void _filterHabitsForSection() {
    // Get habits that belong to this section
    final habitsInSection = _allHabits
        .where((habit) => habit.sectionIds.contains(widget.section.id))
        .toList();

    // CRITICAL FIX: Get the current section from database to ensure we have the latest habitOrder
    final currentSection = _allSections.firstWhere(
      (section) => section.id == widget.section.id,
      orElse: () => widget.section, // Fallback to original if not found
    );

    // Sort habits according to the current section's habitOrder (from database)
    _sectionHabits = _sortHabitsByOrder(habitsInSection, currentSection.habitOrder);
  }

  // CRITICAL FIX: Add dedicated reload method for real-time UI updates
  Future<void> _reloadData() async {
    developer.log('--- _reloadData triggered ---', name: 'HabitsInSectionScreen');
    setState(() {
      _dataFuture = _loadData();
    });
  }

  List<Habit> _sortHabitsByOrder(List<Habit> habits, List<String> habitOrder) {
    if (habitOrder.isEmpty) {
      return habits; // Return as-is if no order specified
    }

    final sortedHabits = <Habit>[];
    
    // First, add habits in the specified order
    for (final habitId in habitOrder) {
      final habit = habits.firstWhere(
        (h) => h.id == habitId,
        orElse: () => Habit(name: ''), // Dummy habit that will be filtered out
      );
      if (habit.name.isNotEmpty) {
        sortedHabits.add(habit);
      }
    }
    
    // Then add any habits not in the order list
    for (final habit in habits) {
      if (!habitOrder.contains(habit.id)) {
        sortedHabits.add(habit);
      }
    }
    
    return sortedHabits;
  }

  Future<void> _onReorder(int oldIndex, int newIndex) async {
    try {
      debugPrint('[SECTION_REORDER] Reordering habit from $oldIndex to $newIndex');
      
      setState(() {
        // Adjust newIndex if moving item down the list
        if (oldIndex < newIndex) {
          newIndex -= 1;
        }
        
        // Update local list
        final habit = _sectionHabits.removeAt(oldIndex);
        _sectionHabits.insert(newIndex, habit);
      });

      // Update the section's habitOrder list
      final newHabitOrder = _sectionHabits.map((habit) => habit.id).toList();
      
      // CRITICAL FIX: Get the current section from database to ensure we have the latest data
      final currentSection = _allSections.firstWhere(
        (section) => section.id == widget.section.id,
        orElse: () => widget.section, // Fallback to original if not found
      );
      
      final updatedSection = currentSection.copyWith(
        habitOrder: newHabitOrder,
      );

      // Save the updated section to the database
      await _databaseService.updateSection(updatedSection);
      
      // THIS IS THE CRITICAL FIX: Reload all data to reflect the new order
      await _reloadData();
      
      debugPrint('[SECTION_REORDER] Successfully updated section habit order');
      
    } catch (e, stackTrace) {
      debugPrint('[ERROR] Failed to reorder habits in section: $e');
      debugPrint('[ERROR] StackTrace: $stackTrace');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reorder habits: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      // Reload data to revert changes
      setState(() {
        _dataFuture = _loadData();
      });
    }
  }

  List<DateTime> _generateDates() {
    final now = DateTime.now();
    final List<DateTime> dateList = [];
    for (int i = 6; i >= 0; i--) { // Show 7 days for compact view
      dateList.add(DateTime(now.year, now.month, now.day - i));
    }
    return dateList.reversed.toList();
  }

  // Calculate completion percentage for the section
  double _calculateCompletionPercentage() {
    debugPrint('[DEBUG] _calculateCompletionPercentage: Starting calculation');
    debugPrint('[DEBUG] Section habits count: ${_sectionHabits.length}');
    
    if (_sectionHabits.isEmpty) {
      debugPrint('[DEBUG] No habits in section, returning 0.0');
      return 0.0;
    }
    
    final today = DateTime.now();
    final todayDateTime = DateTime(today.year, today.month, today.day);
    debugPrint('[DEBUG] Today DateTime: $todayDateTime');
    
    int completedCount = 0;
    int totalCount = _sectionHabits.length;
    
    for (final habit in _sectionHabits) {
      debugPrint('[DEBUG] Checking habit: ${habit.name}');
      debugPrint('[DEBUG] Habit completions: ${habit.completions}');
      
      // Fixed: Use the correct method to check completion
      final isCompleted = habit.isCompletedOnDate(todayDateTime);
      debugPrint('[DEBUG] Habit ${habit.name} completed today: $isCompleted');
      
      if (isCompleted) {
        completedCount++;
      }
    }
    
    final percentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0.0;
    debugPrint('[DEBUG] Completion calculation: $completedCount/$totalCount = $percentage%');
    
    return percentage;
  }

  Color _getSectionColor() {
    try {
      return Color(int.parse(widget.section.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      return isDark ? ModernTheme.darkAccent : ModernTheme.lightAccent;
    }
  }

  Widget _buildRefinedHeader() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final sectionColor = _getSectionColor();
    final completionPercentage = _calculateCompletionPercentage();
    
    return Row(
      children: [
        // Section name
        Text(
          widget.section.name,
          style: GoogleFonts.inter(
            fontSize: 18, // 10% reduction from 20
            fontWeight: FontWeight.w600,
            color: isDark ? ModernTheme.darkTextPrimary : ModernTheme.lightTextPrimary,
          ),
        ),
        
        SizedBox(width: ModernTheme.spaceSM),
        
        // Completion percentage with up arrow
        Row(
          children: [
            Icon(
              Icons.keyboard_arrow_up,
              size: 16,
              color: sectionColor,
            ),
            Text(
              '${completionPercentage.round()}%',
              style: GoogleFonts.inter(
                fontSize: 14.4, // 10% reduction from 16
                fontWeight: FontWeight.w500,
                color: sectionColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    developer.log('--- Build method called for HabitsInSectionScreen ---', name: 'HabitsInSectionScreen');
    developer.log('Displaying ${_sectionHabits.length} habits in table view.', name: 'HabitsInSectionScreen');
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: _buildRefinedHeader(),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
          color: Theme.of(context).appBarTheme.iconTheme?.color,
        ),
      ),
      body: SafeArea(
        child: FutureBuilder<void>(
          future: _dataFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text('Error loading data: ${snapshot.error}'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _dataFuture = _loadData();
                        });
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            } else if (_sectionHabits.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.library_add_check_outlined,
                      size: 60,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No habits in "${widget.section.name}"',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add habits to this section to see them here',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            // Build the table view with section habits
            final dates = _generateDates();
            
            return Expanded(
              child: Padding(
                padding: EdgeInsets.all(ModernTheme.spaceMD), // 25% reduction: 12px from 16px
                child: Builder(
                  builder: (context) {
                    developer.log('=== BUILDING EnhancedHabitTableView in Section ===', name: 'HabitsInSectionScreen');
                    developer.log('Section habits count: ${_sectionHabits.length}', name: 'HabitsInSectionScreen');
                    developer.log('Dates count: ${dates.length}', name: 'HabitsInSectionScreen');
                    developer.log('All sections count: ${_allSections.length}', name: 'HabitsInSectionScreen');
                    
                    try {
                      return EnhancedHabitTableView(
                        habits: _sectionHabits,
                        dates: dates,
                        sections: _allSections,
                        onReorder: _onReorder,
                        showReorderDialog: true,
                        showPercentageRow: false, // Remove percentage row for cleaner section view
                        onDataChanged: _reloadData, // THIS IS THE CRITICAL FIX: Reload data after habit completion toggles
                      );
                    } catch (e, stackTrace) {
                      developer.log('ERROR: Failed to create EnhancedHabitTableView - $e', name: 'HabitsInSectionScreen');
                      developer.log('StackTrace: $stackTrace', name: 'HabitsInSectionScreen');
                      return Container(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error, color: Colors.red, size: 48),
                            const SizedBox(height: 16),
                            Text('Error creating section table view: $e'),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => _reloadData(),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      );
                    }
                  },
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddHabitDialog(),
        tooltip: 'Add habit to this section',
        child: const Icon(Icons.add),
      ),
    );
  }

  Future<void> _showAddHabitDialog() async {
    // Import the add habit dialog from modern_habits_screen.dart
    final TextEditingController nameController = TextEditingController();
    final TextEditingController targetController = TextEditingController();
    final TextEditingController unitController = TextEditingController();
    String selectedSectionId = widget.section.id; // Pre-select current section
    HabitType selectedType = HabitType.boolean;

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Container(
                width: 450,
                padding: const EdgeInsets.all(20), // Reduced from 24
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Text(
                      'Add New Habit',
                      style: GoogleFonts.inter(
                        fontSize: 18, // Reduced from 20
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 20), // Reduced from 24

                    // Habit Name
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: 'Habit Name',
                        labelStyle: TextStyle(fontSize: 13), // Reduced from 14
                        hintText: 'e.g., Morning Exercise, Drink Water',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      autofocus: true,
                    ),
                    const SizedBox(height: 14), // Reduced from 16

                    // Habit Type Selection
                    Text(
                      'Habit Type',
                      style: GoogleFonts.inter(
                        fontSize: 13, // Reduced from 14
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 6), // Reduced from 8
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<HabitType>(
                            title: const Text('Yes/No', style: TextStyle(fontSize: 13)),
                            subtitle: const Text('Simple completion', style: TextStyle(fontSize: 11)),
                            value: HabitType.boolean,
                            groupValue: selectedType,
                            onChanged: (value) {
                              setDialogState(() {
                                selectedType = value!;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<HabitType>(
                            title: const Text('Numerical', style: TextStyle(fontSize: 13)),
                            subtitle: const Text('Track amounts', style: TextStyle(fontSize: 11)),
                            value: HabitType.numerical,
                            groupValue: selectedType,
                            onChanged: (value) {
                              setDialogState(() {
                                selectedType = value!;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 14), // Reduced from 16

                    // Numerical habit fields
                    if (selectedType == HabitType.numerical) ...[
                      Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: TextField(
                              controller: targetController,
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              decoration: InputDecoration(
                                labelText: 'Target Value',
                                labelStyle: TextStyle(fontSize: 13),
                                hintText: 'e.g., 8, 30, 10000',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            flex: 1,
                            child: TextField(
                              controller: unitController,
                              decoration: InputDecoration(
                                labelText: 'Unit',
                                labelStyle: TextStyle(fontSize: 13),
                                hintText: 'glasses, minutes, steps',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 14), // Reduced from 16
                    ],

                    // Section Selection (pre-selected to current section)
                    DropdownButtonFormField<String>(
                      value: selectedSectionId,
                      decoration: InputDecoration(
                        labelText: 'Section',
                        labelStyle: TextStyle(fontSize: 13),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: _allSections.map((section) {
                        return DropdownMenuItem<String>(
                          value: section.id,
                          child: Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: Color(int.parse(section.color.replaceFirst('#', '0xFF'))),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(section.name, style: TextStyle(fontSize: 13)),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          selectedSectionId = value;
                        }
                      },
                    ),
                    const SizedBox(height: 20), // Reduced from 24

                    // Actions
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: () async {
                            final habitName = nameController.text.trim();
                            if (habitName.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Please enter a habit name'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            // Validate numerical habit fields
                            double? targetValue;
                            String? unit;
                            if (selectedType == HabitType.numerical) {
                              final targetText = targetController.text.trim();
                              if (targetText.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Please enter a target value'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                                return;
                              }
                              targetValue = double.tryParse(targetText);
                              if (targetValue == null || targetValue <= 0) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Please enter a valid target value'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                                return;
                              }
                              unit = unitController.text.trim().isEmpty ? null : unitController.text.trim();
                            }

                            try {
                              final newHabit = Habit(
                                name: habitName,
                                sectionIds: [selectedSectionId],
                                type: selectedType,
                                targetValue: targetValue,
                                unit: unit,
                              );

                              await _databaseService.addHabit(newHabit);
                              await _reloadData();

                              if (mounted) {
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Added habit: $habitName'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Failed to add habit: $e'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          },
                          child: const Text('Add Habit'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}