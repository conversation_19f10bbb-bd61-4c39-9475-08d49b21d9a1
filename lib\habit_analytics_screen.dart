import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math' as math;
import 'habit.dart';
import 'entry.dart';
import 'section.dart';
import 'database_service.dart';
import 'enhanced_entry_dialog.dart';
import 'habit_analytics_widgets.dart';
import 'habit_analytics_service.dart';
import 'modern_theme.dart';
import 'modern_widgets.dart';
import 'section_color_palette.dart';

/// Individual Habit Analytics Screen showing comprehensive analytics for a specific habit
class HabitAnalyticsScreen extends StatefulWidget {
  final Habit habit;

  const HabitAnalyticsScreen({
    super.key,
    required this.habit,
  });

  @override
  State<HabitAnalyticsScreen> createState() => _HabitAnalyticsScreenState();
}

class _HabitAnalyticsScreenState extends State<HabitAnalyticsScreen>
    with TickerProviderStateMixin {
  final DatabaseService _databaseService = DatabaseService();
  late Habit _currentHabit;
  List<Entry> _entries = [];
  List<Section> _sections = [];
  late HabitAnalyticsService _analyticsService;
  TimeScale _scoreChartTimeScale = TimeScale.week;
  TimeScale _historyChartTimeScale = TimeScale.week;
  bool _isLoading = true;

  // Task 2: Add ScrollControllers for charts
  late ScrollController _scoreChartController;
  late ScrollController _historyChartController;
  late ScrollController _heatmapController;
  
  @override
  void initState() {
    super.initState();
    _currentHabit = widget.habit;
    
    // Task 2: Initialize ScrollControllers
    _scoreChartController = ScrollController();
    _historyChartController = ScrollController();
    _heatmapController = ScrollController();
    
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final results = await Future.wait([
        _databaseService.loadEntriesForHabit(_currentHabit.id),
        _databaseService.loadAllSections(),
      ]);
      
      _entries = results[0] as List<Entry>;
      _sections = results[1] as List<Section>;
      _analyticsService = HabitAnalyticsService(
        habit: _currentHabit,
        entries: _entries,
      );
      
      // Task 2: Set initial scroll positions after data loads
      _setInitialScrollPositions();
    } catch (e) {
      debugPrint('Error loading habit analytics data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Task 2: Set initial scroll positions to show recent data with 3 weeks of context
  void _setInitialScrollPositions() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToRecentData(_scoreChartController, _scoreChartTimeScale);
      _scrollToRecentData(_historyChartController, _historyChartTimeScale);
      _scrollToRecentData(_heatmapController, TimeScale.day); // Heatmap uses day scale
    });
  }

  /// Task 2: Scroll to show recent data with historical context
  void _scrollToRecentData(ScrollController controller, TimeScale timeScale) {
    if (!controller.hasClients) return;
    
    try {
      final maxScrollExtent = controller.position.maxScrollExtent;
      if (maxScrollExtent <= 0) return;
      
      // Calculate width of one week based on time scale
      double weekWidth;
      switch (timeScale) {
        case TimeScale.day:
          weekWidth = 35.0 * 7; // 7 days * day width
          break;
        case TimeScale.week:
          weekWidth = 50.0; // Week width
          break;
        case TimeScale.month:
          weekWidth = 60.0 * 4; // Approximate 4 weeks per month
          break;
        default:
          weekWidth = 200.0; // Default fallback
      }
      
      // Calculate target offset: show recent data with 3 weeks of context
      final contextWidth = weekWidth * 3;
      final targetOffset = maxScrollExtent - contextWidth;
      final clampedOffset = targetOffset.clamp(0.0, maxScrollExtent);
      
      controller.jumpTo(clampedOffset);
      debugPrint('[HABIT_ANALYTICS_SCREEN] Scrolled ${controller.runtimeType} to offset: $clampedOffset (max: $maxScrollExtent)');
    } catch (e) {
      debugPrint('[HABIT_ANALYTICS_SCREEN] Error setting scroll position: $e');
    }
  }

  Future<void> _refreshData() async {
    // Reload the habit to get updated data
    final updatedHabit = await _databaseService.getHabitById(_currentHabit.id);
    if (updatedHabit != null) {
      setState(() => _currentHabit = updatedHabit);
    }
    await _loadData();
  }

  @override
  void dispose() {
    // Task 2: Dispose ScrollControllers
    _scoreChartController.dispose();
    _historyChartController.dispose();
    _heatmapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] === BUILDING HABIT ANALYTICS SCREEN ===');
    debugPrint('[HABIT_ANALYTICS_SCREEN] Habit: ${_currentHabit.name}');
    debugPrint('[HABIT_ANALYTICS_SCREEN] Loading state: $_isLoading');
    debugPrint('[HABIT_ANALYTICS_SCREEN] Entries count: ${_entries.length}');
    debugPrint('[HABIT_ANALYTICS_SCREEN] Sections count: ${_sections.length}');
    
    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;
    
    debugPrint('[HABIT_ANALYTICS_SCREEN] Theme brightness: ${isDarkTheme ? 'Dark' : 'Light'}');

    return Scaffold(
      appBar: _buildAppBar(theme),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(ModernTheme.spaceSM * 0.95), // Additional 5% reduction
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeaderSection(theme, isDarkTheme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildScoreChart(theme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildHistoryChart(theme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildStreaksAndFrequency(theme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildHeatmapCalendar(theme),
                ],
              ),
            ),
    );
  }

  // Task 1: Modern AppBar implementation
  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Building modern app bar');
    
    return AppBar(
      title: Text(
        _currentHabit.name,
        style: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        ),
      ),
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_new,
          color: theme.colorScheme.onSurface,
          size: 20,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: Icon(
            Icons.refresh,
            color: theme.colorScheme.onSurface,
            size: 22,
          ),
          onPressed: _refreshData,
        ),
      ],
    );
  }

  // Task 1: Restore Header/Overview - compact "Days Since Created" and "Overview" card
  Widget _buildHeaderSection(ThemeData theme, bool isDarkTheme) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Building header section');
    
    final daysSinceCreated = DateTime.now().difference(_currentHabit.createdAt).inDays;
    final currentScore = _analyticsService.calculateScore(TimeScale.week);
    
    return Row(
      children: [
        // Days Since Created Card
        Expanded(
          child: ModernCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Days Since Created',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                SizedBox(height: ModernTheme.spaceXS),
                Text(
                  '$daysSinceCreated',
                  style: GoogleFonts.inter(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: ModernTheme.spaceSM),
        // Overview Card
        Expanded(
          child: ModernCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Score',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                SizedBox(height: ModernTheme.spaceXS),
                Text(
                  '${(currentScore * 100).toInt()}%',
                  style: GoogleFonts.inter(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: _getScoreColor(currentScore, theme),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Task 1: Restore Score Chart with fl_chart and time scale dropdown
  Widget _buildScoreChart(ThemeData theme) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Building Score Chart');
    
    final chartData = _analyticsService.getScoreChartData(_scoreChartTimeScale);
    final chartWidth = _calculateChartWidth(chartData.length, _scoreChartTimeScale);
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Score Chart',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              _buildTimeScaleDropdown(_scoreChartTimeScale, (newScale) {
                setState(() {
                  _scoreChartTimeScale = newScale;
                });
                _scrollToRecentData(_scoreChartController, newScale);
              }),
            ],
          ),
          SizedBox(height: ModernTheme.spaceSM),
          // TASK 1 & 2: Enable horizontal scrolling with viewport sizing for Score Chart
          SizedBox(
            height: 200,
            child: SingleChildScrollView(
              controller: _scoreChartController,
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: chartWidth,
                child: LineChart(_buildScoreLineChart(chartData, theme)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Task 1: Restore History Chart with fl_chart and time scale dropdown
  Widget _buildHistoryChart(ThemeData theme) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Building History Chart');
    
    final chartData = _analyticsService.getHistoryChartData(_historyChartTimeScale);
    final chartWidth = _calculateChartWidth(chartData.length, _historyChartTimeScale);
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'History Chart',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              _buildTimeScaleDropdown(_historyChartTimeScale, (newScale) {
                setState(() {
                  _historyChartTimeScale = newScale;
                });
                _scrollToRecentData(_historyChartController, newScale);
              }),
            ],
          ),
          SizedBox(height: ModernTheme.spaceSM),
          // TASK 1 & 2: Enable horizontal scrolling with viewport sizing for History Chart
          SizedBox(
            height: 200,
            child: SingleChildScrollView(
              controller: _historyChartController,
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: chartWidth,
                child: BarChart(_buildHistoryBarChart(chartData, theme)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Task 1: Restore Streaks & Frequency Card
  Widget _buildStreaksAndFrequency(ThemeData theme) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Building Streaks & Frequency');
    
    final currentStreak = _analyticsService.getCurrentStreak();
    final bestStreak = _analyticsService.getBestStreak();
    final totalCompletions = _analyticsService.getTotalCompletions();
    final frequencyByDay = _analyticsService.getFrequencyByDay();
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Streaks & Frequency',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: ModernTheme.spaceSM),
          
          // Streak Cards Row
          Row(
            children: [
              Expanded(
                child: _buildStreakCard('Current Streak', '${currentStreak.days} days', theme),
              ),
              SizedBox(width: ModernTheme.spaceXS),
              Expanded(
                child: _buildStreakCard('Best Streak', '${bestStreak.days} days', theme),
              ),
              SizedBox(width: ModernTheme.spaceXS),
              Expanded(
                child: _buildStreakCard('Total', '$totalCompletions', theme),
              ),
            ],
          ),
          
          SizedBox(height: ModernTheme.spaceSM),
          
          // Frequency by Day Chips
          Text(
            'Frequency by Day',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: ModernTheme.spaceXS),
          Wrap(
            spacing: ModernTheme.spaceXS,
            runSpacing: ModernTheme.spaceXS,
            children: frequencyByDay.entries.map((entry) {
              return _buildFrequencyChip(entry.key, entry.value, theme);
            }).toList(),
          ),
        ],
      ),
    );
  }

  // Task 1: Restore Activity Heatmap
  Widget _buildHeatmapCalendar(ThemeData theme) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Building Activity Heatmap');
    
    final heatmapData = _analyticsService.getHeatmapData();
    final heatmapWidth = _calculateHeatmapWidth();
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Activity Heatmap',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: ModernTheme.spaceSM),
          SizedBox(
            height: 180, // Fixed height for heatmap
            child: SingleChildScrollView(
              controller: _heatmapController,
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: heatmapWidth,
                child: _buildHeatmapGrid(heatmapData, theme),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build the heatmap grid
  Widget _buildHeatmapGrid(Map<DateTime, double> heatmapData, ThemeData theme) {
    final now = DateTime.now();
    final startDate = DateTime(now.year - 1, now.month, now.day);
    final daysList = <DateTime>[];
    
    // Generate list of dates for the past year
    for (int i = 0; i < 365; i++) {
      daysList.add(startDate.add(Duration(days: i)));
    }
    
    // Group by weeks
    final weeks = <List<DateTime>>[];
    for (int i = 0; i < daysList.length; i += 7) {
      final weekEnd = math.min(i + 7, daysList.length);
      weeks.add(daysList.sublist(i, weekEnd));
    }
    
    return Column(
      children: [
        // Weekday labels
        Row(
          children: [
            SizedBox(width: 30), // Space for month labels
            ...['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) =>
              SizedBox(
                width: 15,
                child: Text(
                  day,
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        // Heatmap grid
        Expanded(
          child: Row(
            children: weeks.map((week) => _buildWeekColumn(week, heatmapData, theme)).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildWeekColumn(List<DateTime> week, Map<DateTime, double> heatmapData, ThemeData theme) {
    return Column(
      children: week.map((date) {
        final intensity = heatmapData[date] ?? 0.0;
        return Container(
          width: 13,
          height: 13,
          margin: EdgeInsets.all(1),
          decoration: BoxDecoration(
            color: _getHeatmapColor(intensity, theme),
            borderRadius: BorderRadius.circular(2),
          ),
        );
      }).toList(),
    );
  }

  Color _getHeatmapColor(double intensity, ThemeData theme) {
    if (intensity == 0) return theme.colorScheme.surfaceVariant;
    final baseColor = theme.colorScheme.primary;
    return Color.lerp(baseColor.withOpacity(0.2), baseColor, intensity) ?? baseColor;
  }

  // Helper methods for building components
  Widget _buildStreakCard(String label, String value, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(ModernTheme.spaceXS),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onSurface,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 10,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencyChip(String day, int count, ThemeData theme) {
    return Chip(
      label: Text(
        '$day: $count',
        style: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: theme.colorScheme.surfaceVariant,
      side: BorderSide.none,
    );
  }

  Widget _buildTimeScaleDropdown(TimeScale currentScale, Function(TimeScale) onChanged) {
    return DropdownButton<TimeScale>(
      value: currentScale,
      onChanged: (TimeScale? newValue) {
        if (newValue != null) {
          onChanged(newValue);
        }
      },
      items: TimeScale.values.map<DropdownMenuItem<TimeScale>>((TimeScale value) {
        return DropdownMenuItem<TimeScale>(
          value: value,
          child: Text(
            value.name.toUpperCase(),
            style: GoogleFonts.inter(fontSize: 12),
          ),
        );
      }).toList(),
      underline: Container(),
      isDense: true,
    );
  }

  // Chart building methods (simplified versions - full implementations would come from habit_details_screen.dart)
  LineChartData _buildScoreLineChart(List<ChartDataPoint> data, ThemeData theme) {
    return LineChartData(
      gridData: FlGridData(show: false),
      titlesData: FlTitlesData(show: false),
      borderData: FlBorderData(show: false),
      lineBarsData: [
        LineChartBarData(
          spots: data.asMap().entries.map((entry) {
            return FlSpot(entry.key.toDouble(), entry.value.value);
          }).toList(),
          isCurved: false,
          color: theme.colorScheme.primary,
          barWidth: 2,
          dotData: FlDotData(show: true),
        ),
      ],
    );
  }

  BarChartData _buildHistoryBarChart(List<ChartDataPoint> data, ThemeData theme) {
    return BarChartData(
      gridData: FlGridData(show: false),
      titlesData: FlTitlesData(show: false),
      borderData: FlBorderData(show: false),
      barGroups: data.asMap().entries.map((entry) {
        return BarChartGroupData(
          x: entry.key,
          barRods: [
            BarChartRodData(
              toY: entry.value.value,
              color: theme.colorScheme.primary,
              width: 16,
            ),
          ],
        );
      }).toList(),
    );
  }

  // Helper calculation methods
  double _calculateChartWidth(int dataPoints, TimeScale timeScale) {
    double pointWidth;
    switch (timeScale) {
      case TimeScale.day:
        pointWidth = 35.0;
        break;
      case TimeScale.week:
        pointWidth = 50.0;
        break;
      case TimeScale.month:
        pointWidth = 60.0;
        break;
      default:
        pointWidth = 40.0;
    }
    return math.max(dataPoints * pointWidth, 300.0);
  }

  double _calculateHeatmapWidth() {
    return 53 * 15.0; // 53 weeks * 15px per week
  }

  Color _getScoreColor(double score, ThemeData theme) {
    if (score >= 0.8) return Colors.green;
    if (score >= 0.6) return Colors.blue;
    if (score >= 0.4) return Colors.orange;
    return Colors.red;
  }
}

