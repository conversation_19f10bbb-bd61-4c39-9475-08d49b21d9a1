import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;
import 'habit.dart';
import 'section.dart';
import 'database_service.dart';
import 'manage_sections_screen.dart';
import 'settings_screen.dart';
import 'settings_service.dart';
import 'theme_notifier.dart';
import 'enhanced_habit_table_view.dart';
import 'modern_widgets.dart';
import 'modern_theme.dart';
import 'color_selection_widget.dart';
import 'section_color_palette.dart';
import 'enhanced_entry_dialog.dart';
import 'habit_analytics_widgets.dart';
import 'entry.dart';

class ModernHabitsScreen extends StatefulWidget {
  const ModernHabitsScreen({super.key});

  @override
  State<ModernHabitsScreen> createState() => _ModernHabitsScreenState();
}

class _ModernHabitsScreenState extends State<ModernHabitsScreen> {
  final _databaseService = DatabaseService();
  final _settingsService = SettingsService.instance;

  // State variables
  late Future<void> _dataFuture;
  List<Habit> _allHabits = [];
  List<Section> _allSections = [];
  String? _selectedSectionId;
  List<Habit> _displayedHabits = [];
  // TASK 2: Add state variable for show/hide completed habits
  bool _showCompleted = false;

  @override
  void initState() {
    super.initState();
    _dataFuture = _loadAllData();
    _loadShowCompletedPreference();
  }

  /// Load the show completed preference from settings
  Future<void> _loadShowCompletedPreference() async {
    try {
      final showCompleted = await _settingsService.loadShowCompleted();
      setState(() {
        _showCompleted = showCompleted;
      });
      _filterHabits(); // Re-filter with loaded preference
    } catch (e) {
      debugPrint('[MODERN_HABITS_SCREEN] Error loading show completed preference: $e');
    }
  }

  /// Save the show completed preference to settings
  Future<void> _saveShowCompletedPreference() async {
    try {
      await _settingsService.saveShowCompleted(_showCompleted);
    } catch (e) {
      debugPrint('[MODERN_HABITS_SCREEN] Error saving show completed preference: $e');
    }
  }

  Future<void> _loadAllData() async {
    try {
      developer.log('=== STARTING DATA LOAD ===', name: 'ModernHabitsScreen.DataFlow');
      
      final results = await Future.wait([
        _databaseService.loadAllSections(),
        _databaseService.loadAllHabits(), // CRITICAL FIX: Use loadAllHabits instead of loadAllHabitsWithEntries
      ]);

      _allSections = results[0] as List<Section>;
      _allHabits = results[1] as List<Habit>;

      developer.log('Loaded ${_allHabits.length} total habits from database.', name: 'ModernHabitsScreen.DataFlow');
      developer.log('Loaded ${_allSections.length} total sections from database.', name: 'ModernHabitsScreen.DataFlow');
      
      // CRITICAL DEBUG: Check if database is completely empty
      if (_allHabits.isEmpty) {
        developer.log('CRITICAL WARNING: No habits found in database! This could indicate:', name: 'ModernHabitsScreen.DataFlow');
        developer.log('1. Database is empty (no habits have been created)', name: 'ModernHabitsScreen.DataFlow');
        developer.log('2. Database loading method is failing silently', name: 'ModernHabitsScreen.DataFlow');
        developer.log('3. Database file path or permissions issue', name: 'ModernHabitsScreen.DataFlow');
        
        // Test direct database access
        try {
          await _databaseService.printDatabaseInfo();
        } catch (e) {
          developer.log('ERROR: Failed to print database info - $e', name: 'ModernHabitsScreen.DataFlow');
        }
      }
      
      // Log each habit for detailed debugging
      for (int i = 0; i < _allHabits.length; i++) {
        final habit = _allHabits[i];
        developer.log('Habit $i: "${habit.name}" (ID: ${habit.id}, Sections: ${habit.sectionIds})', name: 'ModernHabitsScreen.DataFlow');
      }

      _filterHabits();
      developer.log('=== DATA LOAD COMPLETE ===', name: 'ModernHabitsScreen.DataFlow');
    } catch (e, stackTrace) {
      developer.log('ERROR: Failed to load data - $e', name: 'ModernHabitsScreen.DataFlow');
      developer.log('StackTrace: $stackTrace', name: 'ModernHabitsScreen.DataFlow');
      debugPrint('ERROR: Failed to load data - $e');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  void _filterHabits() {
    developer.log('=== STARTING HABIT FILTERING ===', name: 'ModernHabitsScreen.DataFlow');
    developer.log('Filtering ${_allHabits.length} habits. Selected section: $_selectedSectionId, Show completed: $_showCompleted', name: 'ModernHabitsScreen.DataFlow');
    
    final today = DateTime.now();
    
    if (_selectedSectionId == null) {
      developer.log('No section selected - showing all habits', name: 'ModernHabitsScreen.DataFlow');
      // TASK 2: Apply show/hide completed filter
      if (_showCompleted) {
        _displayedHabits = List.from(_allHabits);
        developer.log('Show completed = true: Displaying all ${_allHabits.length} habits', name: 'ModernHabitsScreen.DataFlow');
      } else {
        _displayedHabits = _allHabits
            .where((habit) => !habit.isCompletedOnDate(today))
            .toList();
        developer.log('Show completed = false: Filtering out completed habits', name: 'ModernHabitsScreen.DataFlow');
      }
    } else {
      developer.log('Section selected: $_selectedSectionId - filtering by section', name: 'ModernHabitsScreen.DataFlow');
      // TASK 2: Filter by section AND apply show/hide completed filter
      if (_showCompleted) {
        _displayedHabits = _allHabits
            .where((habit) => habit.sectionIds.contains(_selectedSectionId))
            .toList();
        developer.log('Show completed = true: Displaying habits in section $_selectedSectionId', name: 'ModernHabitsScreen.DataFlow');
      } else {
        _displayedHabits = _allHabits
            .where((habit) => 
                habit.sectionIds.contains(_selectedSectionId) && 
                !habit.isCompletedOnDate(today))
            .toList();
        developer.log('Show completed = false: Displaying incomplete habits in section $_selectedSectionId', name: 'ModernHabitsScreen.DataFlow');
      }
    }
    
    developer.log('Filter result: ${_displayedHabits.length} habits will be displayed.', name: 'ModernHabitsScreen.DataFlow');
    
    // Log each displayed habit for detailed debugging
    for (int i = 0; i < _displayedHabits.length; i++) {
      final habit = _displayedHabits[i];
      developer.log('Displayed Habit $i: "${habit.name}" (ID: ${habit.id})', name: 'ModernHabitsScreen.DataFlow');
    }
    
    developer.log('=== HABIT FILTERING COMPLETE ===', name: 'ModernHabitsScreen.DataFlow');
  }

  // TASK 3: Enhanced _reloadData to ensure percentage updates
  Future<void> _reloadData() async {
    developer.log('--- _reloadData triggered ---', name: 'ModernHabitsScreen');
    developer.log('Loading habits for section: ${_selectedSectionId ?? 'All Habits'}', name: 'ModernHabitsScreen');
    debugPrint('[MODERN_HABITS_SCREEN] === RELOADING DATA ===');
    try {
      // Reload all data from database
      final results = await Future.wait([
        _databaseService.loadAllSections(),
        _databaseService.loadAllHabits(), // CRITICAL FIX: Use loadAllHabits instead of loadAllHabitsWithEntries
      ]);

      // Update state with fresh data
      _allSections = results[0] as List<Section>;
      _allHabits = results[1] as List<Habit>;
      
      developer.log('Total habits loaded: ${_allHabits.length}', name: 'ModernHabitsScreen');
      
      // Recalculate filtered habits
      _filterHabits();
      
      debugPrint('[MODERN_HABITS_SCREEN] Data reloaded - Total habits: ${_allHabits.length}, Displayed: ${_displayedHabits.length}');
      
      // TASK 3: Force UI rebuild to update percentages
      setState(() {
        // State updated - this will trigger rebuild of entire screen including header percentages
      });
    } catch (e, stackTrace) {
      debugPrint('[MODERN_HABITS_SCREEN] ERROR: Failed to reload data - $e');
      debugPrint('[MODERN_HABITS_SCREEN] StackTrace: $stackTrace');
      
      // Still update the future to show error state
      setState(() {
        _dataFuture = Future.error(e);
      });
    }
  }

  Future<void> _onReorder(int oldIndex, int newIndex) async {
    try {
      debugPrint('[HABITS_SCREEN] Reordering habit from $oldIndex to $newIndex');
      
      setState(() {
        if (oldIndex < newIndex) {
          newIndex -= 1;
        }
        
        final habit = _displayedHabits.removeAt(oldIndex);
        _displayedHabits.insert(newIndex, habit);
        
        _updateMasterHabitsOrder();
      });

      await _databaseService.saveAllHabits(_allHabits);
      await _reloadData();
      
      debugPrint('[HABITS_SCREEN] Successfully updated habit order');
      
    } catch (e, stackTrace) {
      debugPrint('[ERROR] Failed to reorder habits: $e');
      debugPrint('[ERROR] StackTrace: $stackTrace');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reorder habits: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      await _reloadData();
    }
  }

  void _updateMasterHabitsOrder() {
    final List<Habit> newOrderedHabits = [];

    for (final displayedHabit in _displayedHabits) {
      final masterHabitIndex = _allHabits.indexWhere(
        (h) => h.id == displayedHabit.id,
      );
      if (masterHabitIndex != -1) {
        newOrderedHabits.add(_allHabits[masterHabitIndex]);
      }
    }

    for (final masterHabit in _allHabits) {
      if (!_displayedHabits.any((h) => h.id == masterHabit.id)) {
        newOrderedHabits.add(masterHabit);
      }
    }

    _allHabits.clear();
    _allHabits.addAll(newOrderedHabits);
  }

  Future<void> _showAddHabitDialog() async {
    if (_allSections.isEmpty) {
      final defaultSection = Section(name: 'My Habits');
      _allSections.add(defaultSection);
      await _databaseService.addSection(defaultSection);
    }

    final TextEditingController nameController = TextEditingController();
    final TextEditingController targetController = TextEditingController();
    final TextEditingController unitController = TextEditingController();
    String selectedSectionId = _allSections.first.id;
    HabitType selectedType = HabitType.boolean;

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Container(
                width: 450,
                padding: const EdgeInsets.all(20), // Reduced from 24
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Text(
                      'Add New Habit',
                      style: GoogleFonts.inter(
                        fontSize: 18, // Reduced from 20
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 20), // Reduced from 24

                    // Habit Name
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: 'Habit Name',
                        hintText: 'e.g., Morning Exercise, Drink Water',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      autofocus: true,
                    ),
                    const SizedBox(height: 16),

                    // Habit Type Selection
                    Text(
                      'Habit Type',
                      style: GoogleFonts.inter(
                        fontSize: 13, // Reduced from 14
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<HabitType>(
                            title: const Text('Yes/No'),
                            subtitle: const Text('Simple completion'),
                            value: HabitType.boolean,
                            groupValue: selectedType,
                            onChanged: (value) {
                              setDialogState(() {
                                selectedType = value!;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<HabitType>(
                            title: const Text('Numerical'),
                            subtitle: const Text('Track amounts'),
                            value: HabitType.numerical,
                            groupValue: selectedType,
                            onChanged: (value) {
                              setDialogState(() {
                                selectedType = value!;
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 14), // Reduced from 16

                    // Numerical habit fields
                    if (selectedType == HabitType.numerical) ...[
                      Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: TextField(
                              controller: targetController,
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                              ],
                              decoration: InputDecoration(
                                labelText: 'Target Value',
                                hintText: 'e.g., 8, 30, 10000',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            flex: 1,
                            child: TextField(
                              controller: unitController,
                              decoration: InputDecoration(
                                labelText: 'Unit',
                                hintText: 'glasses, minutes, steps',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 14), // Reduced from 16
                    ],

                    // Section Selection
                    DropdownButtonFormField<String>(
                      value: selectedSectionId,
                      decoration: InputDecoration(
                        labelText: 'Section',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: _allSections.map((section) {
                        return DropdownMenuItem<String>(
                          value: section.id,
                          child: Row(
                            children: [
                              SectionColorChip(
                                colorHex: section.color,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(section.name),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          selectedSectionId = value;
                        }
                      },
                    ),
                    const SizedBox(height: 20), // Reduced from 24

                    // Actions
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: () async {
                            final habitName = nameController.text.trim();
                            if (habitName.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Please enter a habit name'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            // Validate numerical habit fields
                            double? targetValue;
                            String? unit;
                            if (selectedType == HabitType.numerical) {
                              final targetText = targetController.text.trim();
                              if (targetText.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Please enter a target value'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                                return;
                              }
                              targetValue = double.tryParse(targetText);
                              if (targetValue == null || targetValue <= 0) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Please enter a valid target value'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                                return;
                              }
                              unit = unitController.text.trim().isEmpty ? null : unitController.text.trim();
                            }

                            try {
                              final newHabit = Habit(
                                name: habitName,
                                sectionIds: [selectedSectionId],
                                type: selectedType,
                                targetValue: targetValue,
                                unit: unit,
                              );

                              developer.log('=== ADDING NEW HABIT ===', name: 'ModernHabitsScreen.HabitCreation');
                              developer.log('Creating habit: "${habitName}" with type: ${selectedType}', name: 'ModernHabitsScreen.HabitCreation');
                              
                              await _databaseService.addHabit(newHabit);
                              developer.log('Habit added to database successfully', name: 'ModernHabitsScreen.HabitCreation');
                              
                              await _reloadData();
                              developer.log('Data reloaded after habit creation', name: 'ModernHabitsScreen.HabitCreation');

                              if (mounted) {
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Added habit: $habitName'),
                                    backgroundColor: Theme.of(context).brightness == Brightness.light
                                        ? ModernTheme.lightSuccess
                                        : ModernTheme.darkSuccess,
                                  ),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Failed to add habit: $e'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          },
                          child: const Text('Add Habit'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    developer.log('=== BUILD METHOD CALLED FOR MODERNHABITSSCREEN ===', name: 'ModernHabitsScreen.Build');
    developer.log('Current state - All habits: ${_allHabits.length}, Displayed habits: ${_displayedHabits.length}', name: 'ModernHabitsScreen.Build');
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Streamlined Header Card
            _buildStreamlinedHeader(),
            
            // Main content - maximized table space
            Expanded(
              child: FutureBuilder<void>(
                future: _dataFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (snapshot.hasError) {
                    return _buildErrorState(snapshot.error.toString());
                  } else if (_displayedHabits.isEmpty) {
                    return _buildEmptyState();
                  }

                  return _buildOptimizedHabitsTable();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreamlinedHeader() {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      // TASK 1: Reduce vertical padding by 20% (from 10 to 8)
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.black.withOpacity(0.05)
                : Colors.black.withOpacity(0.15),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // TASK 3: Add day percentage display
          _buildDayPercentageHeader(theme),
          const SizedBox(height: 8),
          // TASK 1: Redesigned header with only 3 main widgets
          Row(
            children: [
              // Section Selection Dropdown
              Expanded(
                child: _buildCompactSectionSelector(),
              ),
              
              const SizedBox(width: 12),
              
              // Add Habit Button
              _buildCompactIconButton(
                icon: Icons.add,
                onPressed: _showAddHabitDialog,
                tooltip: 'Add new habit',
              ),
              
              const SizedBox(width: 8),
              
              // TASK 1: PopupMenuButton with all secondary actions
              _buildPopupMenuButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompactSectionSelector() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.08),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: _selectedSectionId,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: theme.colorScheme.primary,
            size: 18,
          ),
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.primary,
          ),
          dropdownColor: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(10),
          onChanged: _onSectionSelected,
          items: [
            DropdownMenuItem(
              value: null,
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    "All Habits",
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            ..._allSections.map<DropdownMenuItem<String>>((section) {
              final isDarkTheme = theme.brightness == Brightness.dark;
              final sectionColor = SectionColorPalette.getColorFromHex(
                section.color, 
                isDarkTheme: isDarkTheme
              );
              
              return DropdownMenuItem<String>(
                value: section.id,
                child: Row(
                  children: [
                    SectionColorChip(
                      colorHex: section.color,
                      size: 12,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      section.name,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  // TASK 1: New PopupMenuButton to replace individual action buttons
  Widget _buildPopupMenuButton() {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.black.withOpacity(0.04)
                : Colors.black.withOpacity(0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: PopupMenuButton<String>(
        icon: Icon(
          Icons.more_vert,
          size: 20,
          color: theme.colorScheme.onSurface,
        ),
        tooltip: 'More options',
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 36,
          minHeight: 36,
        ),
        onSelected: _handlePopupMenuSelection,
        itemBuilder: (context) => [
          PopupMenuItem<String>(
            value: 'toggle_theme',
            child: Row(
              children: [
                Icon(
                  theme.brightness == Brightness.dark
                      ? Icons.light_mode
                      : Icons.dark_mode,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(theme.brightness == Brightness.dark
                    ? 'Light Mode'
                    : 'Dark Mode'),
              ],
            ),
          ),
          PopupMenuItem<String>(
            value: 'toggle_completed',
            child: Row(
              children: [
                Icon(
                  _showCompleted ? Icons.visibility_off : Icons.visibility,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(_showCompleted ? 'Hide Completed' : 'Show Completed'),
              ],
            ),
          ),
          const PopupMenuItem<String>(
            value: 'manage_sections',
            child: Row(
              children: [
                Icon(Icons.folder_outlined, size: 20),
                SizedBox(width: 12),
                Text('Manage Sections'),
              ],
            ),
          ),
          const PopupMenuItem<String>(
            value: 'settings',
            child: Row(
              children: [
                Icon(Icons.settings, size: 20),
                SizedBox(width: 12),
                Text('Settings'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // TASK 1 & 2: Handle popup menu selections
  void _handlePopupMenuSelection(String value) {
    switch (value) {
      case 'toggle_theme':
        final notifier = Provider.of<ThemeNotifier>(context, listen: false);
        notifier.toggleTheme();
        break;
      case 'toggle_completed':
        // TASK 2: Toggle show/hide completed habits
        setState(() {
          _showCompleted = !_showCompleted;
          _filterHabits();
        });
        // Save the preference
        _saveShowCompletedPreference();
        break;
      case 'manage_sections':
        _showManageSectionsScreen();
        break;
      case 'settings':
        _showSettingsScreen();
        break;
    }
  }

  // PHASE 2: Enhanced header with proper calendar week average
  Widget _buildDayPercentageHeader(ThemeData theme) {
    final today = DateTime.now();
    final todayPercentage = _calculateCompletionPercentage(today);
    final isDarkTheme = theme.brightness == Brightness.dark;
    final todayColor = getPercentageColor(todayPercentage, isDarkTheme: isDarkTheme);
    
    return FutureBuilder<int>(
      future: _calculateWeeklyAverage(),
      builder: (context, snapshot) {
        final weeklyAverage = snapshot.data ?? 0;
        final weeklyColor = getPercentageColor(weeklyAverage, isDarkTheme: isDarkTheme);
        
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Today's percentage
            Icon(
              Icons.today,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 6),
            Text(
              'Today: ',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              '$todayPercentage%',
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: todayColor,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '(${_getCompletedHabitsCount()}/${_getTotalHabitsCount()})',
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            
            // Separator
            const SizedBox(width: 16),
            Container(
              width: 1,
              height: 16,
              color: theme.colorScheme.outline.withOpacity(0.3),
            ),
            const SizedBox(width: 16),
            
            // PHASE 2: Calendar week average
            Icon(
              Icons.calendar_view_week,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 6),
            Text(
              'This Week: ',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              '$weeklyAverage%',
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: weeklyColor,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCompactIconButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.black.withOpacity(0.04)
                : Colors.black.withOpacity(0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        tooltip: tooltip,
        color: theme.colorScheme.onSurface,
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 36,
          minHeight: 36,
        ),
      ),
    );
  }


  Widget _buildOptimizedHabitsTable() {
    final dates = _generateDates();

    return Container(
      margin: const EdgeInsets.fromLTRB(12, 4, 12, 12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.light
                ? Colors.black.withOpacity(0.05)
                : Colors.black.withOpacity(0.15),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Builder(
          builder: (context) {
            developer.log('=== BUILDING EnhancedHabitTableView ===', name: 'ModernHabitsScreen');
            developer.log('Habits count: ${_displayedHabits.length}', name: 'ModernHabitsScreen');
            developer.log('Dates count: ${dates.length}', name: 'ModernHabitsScreen');
            developer.log('Sections count: ${_allSections.length}', name: 'ModernHabitsScreen');
            
            try {
              developer.log('=== BUILDING ENHANCED HABIT TABLE VIEW ===', name: 'ModernHabitsScreen.Build');
              developer.log('Build: Passing ${_displayedHabits.length} habits to EnhancedHabitTableView.', name: 'ModernHabitsScreen.Build');
              developer.log('Build: Passing ${dates.length} dates to EnhancedHabitTableView.', name: 'ModernHabitsScreen.Build');
              developer.log('Build: Passing ${_allSections.length} sections to EnhancedHabitTableView.', name: 'ModernHabitsScreen.Build');
              
              // Log the actual habits being passed
              for (int i = 0; i < _displayedHabits.length; i++) {
                final habit = _displayedHabits[i];
                developer.log('Passing Habit $i: "${habit.name}" (ID: ${habit.id})', name: 'ModernHabitsScreen.Build');
              }
              
              return EnhancedHabitTableView(
                habits: _displayedHabits, // This is the list to verify
                dates: dates,
                sections: _allSections,
                onReorder: _onReorder,
                showReorderDialog: true,
                onDataChanged: _reloadData,
              );
            } catch (e, stackTrace) {
              developer.log('ERROR: Failed to create EnhancedHabitTableView - $e', name: 'ModernHabitsScreen.Build');
              developer.log('StackTrace: $stackTrace', name: 'ModernHabitsScreen.Build');
              return Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 48),
                    const SizedBox(height: 16),
                    Text('Error creating table view: $e'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _reloadData(),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }


  Widget _buildErrorState(String error) {
    return Center(
      child: ModernCard(
        margin: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading data',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _dataFuture = _loadAllData();
                });
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: ModernCard(
        margin: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.library_add_check_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No habits yet',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Tap the + button to add your first habit',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showAddHabitDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Habit'),
            ),
          ],
        ),
      ),
    );
  }

  List<DateTime> _generateDates() {
    final now = DateTime.now();
    final List<DateTime> dateList = [];
    for (int i = 29; i >= 0; i--) {
      dateList.add(DateTime(now.year, now.month, now.day - i));
    }
    return dateList.reversed.toList();
  }

  // Smart Color Coding System
  Color getPercentageColor(int percentage, {required bool isDarkTheme}) {
    if (percentage == 0) {
      // Neutral gray for 0%
      return isDarkTheme ? const Color(0xFF6B7280) : const Color(0xFF9CA3AF);
    } else if (percentage <= 33) {
      // Tier 1: Low Completion (0-33%) - Red/Orange
      return isDarkTheme ? const Color(0xFFF87171) : const Color(0xFFEF4444);
    } else if (percentage <= 66) {
      // Tier 2: Moderate Completion (34-66%) - Amber/Yellow
      return isDarkTheme ? const Color(0xFFFBBF24) : const Color(0xFFF59E0B);
    } else if (percentage <= 89) {
      // Tier 3: Good Completion (67-89%) - Blue/Teal
      return isDarkTheme ? const Color(0xFF60A5FA) : const Color(0xFF3B82F6);
    } else {
      // Tier 4: Excellent Completion (90-100%) - Green
      return isDarkTheme ? const Color(0xFF34D399) : const Color(0xFF10B981);
    }
  }

  // TASK 3: Fixed percentage calculation to use all habits, not just displayed ones
  int _calculateCompletionPercentage(DateTime date) {
    if (_allHabits.isEmpty) return 0;

    int completedCount = 0;
    for (final habit in _allHabits) {
      // Use both new entry system and legacy completions for accuracy
      if (habit.isCompletedOnDate(date)) {
        completedCount++;
      }
    }

    return ((completedCount / _allHabits.length) * 100).round();
  }

  // TASK 3: Helper method to get completed habits count for today
  int _getCompletedHabitsCount() {
    final today = DateTime.now();
    return _allHabits.where((habit) => habit.isCompletedOnDate(today)).length;
  }

  // TASK 3: Helper method to get total habits count
  int _getTotalHabitsCount() {
    return _allHabits.length;
  }

  // PHASE 2: Calculate weekly average using proper calendar week
  Future<int> _calculateWeeklyAverage() async {
    if (_allHabits.isEmpty) return 0;
    
    final today = DateTime.now();
    
    // Use settings service to get proper week boundaries
    final settingsService = SettingsService.instance;
    final startOfWeekDay = await settingsService.getStartOfWeek();
    
    // Calculate the start of the current week
    DateTime startDate;
    if (startOfWeekDay == SettingsService.SUNDAY) {
      final currentWeekday = today.weekday;
      final daysSinceSunday = currentWeekday == 7 ? 0 : currentWeekday;
      startDate = DateTime(today.year, today.month, today.day - daysSinceSunday);
    } else {
      final currentWeekday = today.weekday;
      final daysSinceMonday = currentWeekday - 1;
      startDate = DateTime(today.year, today.month, today.day - daysSinceMonday);
    }
    
    int totalPercentage = 0;
    int daysCount = 0;
    
    // Calculate average for the current calendar week only
    for (int i = 0; i < 7; i++) {
      final date = startDate.add(Duration(days: i));
      // Only include days up to today
      if (date.isAfter(DateTime(today.year, today.month, today.day))) {
        break;
      }
      
      final dayPercentage = _calculateCompletionPercentage(date);
      totalPercentage += dayPercentage;
      daysCount++;
    }
    
    debugPrint('[MODERN_HABITS_SCREEN] Weekly average calculated: $totalPercentage / $daysCount days = ${daysCount > 0 ? (totalPercentage / daysCount).round() : 0}%');
    
    return daysCount > 0 ? (totalPercentage / daysCount).round() : 0;
  }

  Future<void> _showManageSectionsScreen() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            ManageSectionsScreen(initialSections: _allSections),
      ),
    );

    await _reloadData();
  }

  Future<void> _showSettingsScreen() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );

    // PHASE 2: Reload data after settings change to recalculate week boundaries
    await _reloadData();
  }

  void _onSectionSelected(String? sectionId) {
    setState(() {
      _selectedSectionId = sectionId;
      _filterHabits();
    });
  }
}