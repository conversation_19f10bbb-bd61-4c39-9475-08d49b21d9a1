import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'section.dart';
import 'database_service.dart';
import 'habits_in_section_screen.dart';
import 'color_selection_widget.dart';
import 'section_color_palette.dart';
import 'modern_theme.dart';
import 'habit.dart';

class ManageSectionsScreen extends StatefulWidget {
  final List<Section> initialSections;

  const ManageSectionsScreen({
    super.key,
    required this.initialSections,
  });

  @override
  State<ManageSectionsScreen> createState() => _ManageSectionsScreenState();
}

class _ManageSectionsScreenState extends State<ManageSectionsScreen> {
  final _databaseService = DatabaseService();
  late List<Section> _sections;
  List<Habit> _allHabits = [];
  String? _selectedColor;

  @override
  void initState() {
    super.initState();
    _sections = List.from(widget.initialSections);
    _loadHabits();
  }

  Future<void> _loadHabits() async {
    try {
      _allHabits = await _databaseService.loadAllHabits();
      setState(() {});
    } catch (e) {
      debugPrint('[ERROR] ManageSectionsScreen: Failed to load habits - $e');
    }
  }

  // Helper methods for new design
  int _getHabitCountForSection(String sectionId) {
    return _allHabits.where((habit) => habit.sectionIds.contains(sectionId)).length;
  }

  Color _getSectionColor(Section section) {
    try {
      return Color(int.parse(section.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      return isDark ? ModernTheme.darkAccent : ModernTheme.lightAccent;
    }
  }

  void _handleOverflowAction(String action, Section section) {
    switch (action) {
      case 'rename':
        _showSectionDialog(section: section);
        break;
      case 'color':
        _showColorPickerForSection(section);
        break;
      case 'delete':
        _showDeleteSectionDialog(section);
        break;
    }
  }

  void _showColorPickerForSection(Section section) {
    _selectedColor = section.color;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Change Section Color',
          style: GoogleFonts.inter(
            fontSize: 16.2, // 10% reduction from 18
            fontWeight: FontWeight.w600,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: _buildColorSelectionRow(section),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_selectedColor != null) {
                debugPrint('[DEBUG] _showColorPickerForSection: Creating updated section');
                debugPrint('[DEBUG] Section ID: ${section.id}');
                debugPrint('[DEBUG] Section name: ${section.name}');
                debugPrint('[DEBUG] Selected color: $_selectedColor');
                debugPrint('[DEBUG] Section orderIndex: ${section.orderIndex}');
                debugPrint('[DEBUG] Section habitOrder: ${section.habitOrder}');
                
                final updatedSection = Section(
                  id: section.id,
                  name: section.name,
                  color: _selectedColor!,
                  orderIndex: section.orderIndex, // Fixed: use 'orderIndex' instead of 'order'
                  habitOrder: section.habitOrder,
                );
                
                debugPrint('[DEBUG] Updated section created successfully: ${updatedSection.toString()}');
                await _databaseService.updateSection(updatedSection);
                await _reloadData();
                Navigator.pop(context);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Reload data from database
  Future<void> _reloadData() async {
    try {
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Reloading sections data');
      final results = await Future.wait([
        _databaseService.loadAllSections(),
        _databaseService.loadAllHabits(),
      ]);
      setState(() {
        _sections = results[0] as List<Section>;
        _allHabits = results[1] as List<Habit>;
      });
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Successfully reloaded ${_sections.length} sections');
    } catch (e) {
      debugPrint('[ERROR] ManageSectionsScreen: Failed to reload data - $e');
    }
  }

  // Handle reordering of sections with drag-and-drop
  void _onReorderSections(int oldIndex, int newIndex) async {
    debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Reordering section from index $oldIndex to $newIndex');
    
    setState(() {
      // Adjust newIndex if moving item down the list
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      
      // Update local list: remove from old position and insert at new position
      final Section movedSection = _sections.removeAt(oldIndex);
      _sections.insert(newIndex, movedSection);
      
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Moved section "${movedSection.name}" from $oldIndex to $newIndex');
      
      // Update order indices: loop through reordered list and update orderIndex
      for (int i = 0; i < _sections.length; i++) {
        _sections[i] = _sections[i].copyWith(orderIndex: i);
        debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Updated section "${_sections[i].name}" orderIndex to $i');
      }
    });
    
    // Persist changes to database
    try {
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Saving reordered sections to database');
      await _databaseService.saveAllSections(_sections);
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Successfully saved reordered sections');
    } catch (e) {
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Error saving reordered sections: $e');
      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save section order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Building screen with ${_sections.length} sections');
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Sections'),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Navigating back to main screen');
            Navigator.pop(context);
          },
        ),
      ),
      body: _sections.isEmpty
          ? _buildEmptyState()
          : _buildSectionsList(),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(ModernTheme.spaceLG),
        child: OutlinedButton.icon(
          onPressed: () {
            debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Add section button pressed');
            _showSectionDialog();
          },
          icon: Icon(
            Icons.add,
            size: 18,
            color: Theme.of(context).brightness == Brightness.dark 
                ? ModernTheme.darkAccent 
                : ModernTheme.lightAccent,
          ),
          label: Text(
            'Create New Section',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? ModernTheme.darkAccent 
                  : ModernTheme.lightAccent,
            ),
          ),
          style: OutlinedButton.styleFrom(
            side: BorderSide(
              color: Theme.of(context).brightness == Brightness.dark 
                  ? ModernTheme.darkAccent 
                  : ModernTheme.lightAccent,
              width: 1.5,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: ModernTheme.spaceLG,
              vertical: ModernTheme.spaceMD,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Building empty state');
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_outlined,
            size: 64,
            color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            'No sections yet',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to create your first section',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionsList() {
    debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Building sections list with ${_sections.length} items');
    
    return ReorderableListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _sections.length,
      onReorder: _onReorderSections,
      itemBuilder: (context, index) {
        final section = _sections[index];
        debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Building ListTile for section "${section.name}"');
        
        return _buildRefinedSectionCard(section);
      },
    );
  }

  Widget _buildRefinedSectionCard(Section section) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Get habit count for this section
    final habitCount = _getHabitCountForSection(section.id);
    
    // Get section color or default
    final sectionColor = _getSectionColor(section);
    
    return Card(
      key: ValueKey(section.id), // Required unique key for ReorderableListView
      margin: EdgeInsets.only(bottom: ModernTheme.spaceXS),
      elevation: 1,
      color: isDark ? ModernTheme.darkSurfaceVariant : ModernTheme.lightSurfaceVariant,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: () async {
          debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Section "${section.name}" tapped, navigating to habits view');
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HabitsInSectionScreen(section: section),
            ),
          );
          // Reload data after returning from habits view
          await _reloadData();
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(ModernTheme.spaceMD), // 25% reduction: 12px from 16px
          child: Row(
            children: [
              // Section color indicator
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: sectionColor,
                  shape: BoxShape.circle,
                ),
              ),
              
              SizedBox(width: ModernTheme.spaceMD),
              
              // Section name
              Expanded(
                child: Text(
                  section.name,
                  style: GoogleFonts.inter(
                    fontSize: 14.4, // 10% reduction from 16
                    fontWeight: FontWeight.w500,
                    color: isDark ? ModernTheme.darkTextPrimary : ModernTheme.lightTextPrimary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              SizedBox(width: ModernTheme.spaceSM),
              
              // Habit count badge - refined pill-shaped design
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: ModernTheme.spaceSM, 
                  vertical: ModernTheme.spaceXXS,
                ),
                decoration: BoxDecoration(
                  color: sectionColor.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12), // More rounded for pill shape
                ),
                child: Text(
                  '$habitCount',
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: sectionColor,
                  ),
                ),
              ),
              
              SizedBox(width: ModernTheme.spaceSM),
              
              // Overflow menu
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: isDark ? ModernTheme.darkTextSecondary : ModernTheme.lightTextSecondary,
                  size: 18,
                ),
                onSelected: (value) => _handleOverflowAction(value, section),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'rename',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: ModernTheme.spaceSM),
                        Text('Rename Section'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'color',
                    child: Row(
                      children: [
                        Icon(Icons.palette, size: 16),
                        SizedBox(width: ModernTheme.spaceSM),
                        Text('Change Color'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: theme.colorScheme.error),
                        SizedBox(width: ModernTheme.spaceSM),
                        Text('Delete Section', style: TextStyle(color: theme.colorScheme.error)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSectionDialog({Section? section}) {
    final bool isEditMode = section != null;
    debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Showing ${isEditMode ? 'edit' : 'add'} section dialog');
    if (isEditMode) {
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Editing section "${section.name}" (ID: ${section.id})');
      _selectedColor = section.color;
    } else {
      // Get next available color for new section
      _selectedColor = SectionColorPalette.getNextAvailableColor(
        _sections.map((s) => s.color).toList(),
        isDarkTheme: Theme.of(context).brightness == Brightness.dark,
      );
    }
    
    final TextEditingController nameController = TextEditingController(
      text: isEditMode ? section.name : '', // Pre-fill with current name in edit mode
    );
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isEditMode ? 'Edit Section' : 'Create New Section',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Section Name',
                  border: OutlineInputBorder(),
                  hintText: 'Enter section name...',
                ),
                autofocus: true,
                textCapitalization: TextCapitalization.words,
              ),
              const SizedBox(height: 20),
              _buildColorSelectionRow(isEditMode ? section : null),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: ${isEditMode ? 'Edit' : 'Add'} section dialog cancelled');
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final sectionName = nameController.text.trim();
              if (sectionName.isNotEmpty) {
                if (isEditMode) {
                  debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Updating section "${section.name}" to "$sectionName" with color $_selectedColor');
                  await _updateSection(section, sectionName, _selectedColor!);
                } else {
                  debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Creating new section: "$sectionName" with color $_selectedColor');
                  await _addSection(sectionName, _selectedColor!);
                }
                if (context.mounted) {
                  Navigator.pop(context);
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4F46E5),
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showDeleteSectionDialog(Section section) {
    debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Showing delete dialog for section "${section.name}"');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Section',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to delete the section "${section.name}"? This action cannot be undone.',
          style: GoogleFonts.inter(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () {
              debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Delete section cancelled');
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Deleting section "${section.name}"');
              await _deleteSection(section);
              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildColorSelectionRow(Section? section) {
    final theme = Theme.of(context);
    final currentColor = _selectedColor ?? '#3B82F6';
    
    return InkWell(
      onTap: () => _showColorPicker(),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: theme.dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Text(
              'Section Color',
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const Spacer(),
            SectionColorChip(
              colorHex: currentColor,
              size: 24,
              showBorder: true,
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.keyboard_arrow_right,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  void _showColorPicker() {
    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;
    final usedColors = _sections.map((s) => s.color).toList();
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurfaceVariant.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            // Title
            Text(
              'Select Section Color',
              style: GoogleFonts.inter(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 20),
            
            // Color Selection Widget
            ColorSelectionWidget(
              selectedColor: _selectedColor,
              usedColors: usedColors,
              onColorSelected: (color) {
                setState(() {
                  _selectedColor = color;
                });
                Navigator.pop(context);
              },
              showUsedColorWarning: true,
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Future<void> _addSection(String sectionName, String color) async {
    try {
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: === ADD SECTION PROCESS ===');
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Creating section with name: "$sectionName"');
      
      // Create new section with color
      final newSection = Section(name: sectionName, color: color);
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Created section object (ID: ${newSection.id}) with color: $color');
      
      // Save to database
      await _databaseService.addSection(newSection);
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Saved section to database');
      
      // Update local state
      setState(() {
        _sections.add(newSection);
      });
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Updated local state, total sections: ${_sections.length}');
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section "$sectionName" created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: === ADD SECTION COMPLETE ===');
    } catch (e, stackTrace) {
      debugPrint('[ERROR] ManageSectionsScreen: Failed to add section - $e');
      debugPrint('[ERROR] ManageSectionsScreen: StackTrace - $stackTrace');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create section: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateSection(Section section, String newName, String newColor) async {
    try {
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: === UPDATE SECTION PROCESS ===');
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Updating section "${section.name}" to "$newName"');
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Section ID: ${section.id}');
      
      // Create updated section object with new color
      final updatedSection = section.copyWith(name: newName, color: newColor);
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Created updated section object with color: $newColor');
      
      // Update in database
      await _databaseService.updateSection(updatedSection);
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Updated section in database');
      
      // Update local state
      setState(() {
        final index = _sections.indexWhere((s) => s.id == section.id);
        if (index != -1) {
          _sections[index] = updatedSection;
          debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Updated local state at index $index');
        }
      });
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Local state updated, total sections: ${_sections.length}');
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section updated to "$newName" successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: === UPDATE SECTION COMPLETE ===');
    } catch (e, stackTrace) {
      debugPrint('[ERROR] ManageSectionsScreen: Failed to update section - $e');
      debugPrint('[ERROR] ManageSectionsScreen: StackTrace - $stackTrace');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update section: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteSection(Section section) async {
    try {
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: === DELETE SECTION PROCESS ===');
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Deleting section "${section.name}" (ID: ${section.id})');
      
      // Delete from database
      await _databaseService.deleteSection(section);
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Deleted section from database');
      
      // Update local state
      setState(() {
        _sections.removeWhere((s) => s.id == section.id);
      });
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: Updated local state, remaining sections: ${_sections.length}');
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section "${section.name}" deleted successfully'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      
      debugPrint('[MANAGE_SECTIONS] ManageSectionsScreen: === DELETE SECTION COMPLETE ===');
    } catch (e, stackTrace) {
      debugPrint('[ERROR] ManageSectionsScreen: Failed to delete section - $e');
      debugPrint('[ERROR] ManageSectionsScreen: StackTrace - $stackTrace');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete section: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}